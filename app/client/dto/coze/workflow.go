package coze

type AnalysisWorkflowParams struct {
	DouyinUrl  string `json:"douyin_url"`
	Insight    string `json:"insight"`
	AssetsUUID string `json:"assets_uuid"`
}
type AnalysisWorkflowParamsV2 struct {
	VisionAnalysis string `json:"vision_analysis"`
	VideoTitle     string `json:"video_title"`
	CallbackUrl    string `json:"callback_url"`
}

type TopicsWorkflowParams struct {
	AnalysisResults string `json:"analysis_results"`
	AsrText         string `json:"asr_text"`
	VideoTitle      string `json:"video_title"`
	CommercialPos   string `json:"commercial_pos"`
	AssetsUUID      string `json:"assets_uuid"`
}
type TopicsWorkflowParamsV2 struct {
	CallbackUrl    string `json:"callback_url"`
	VisionAnalysis string `json:"vision_analysis"`
	VideoTitle     string `json:"video_title"`
	TopicTitle     string `json:"topic_title"`
	CommercialPos  string `json:"commercial_pos"`
	ScriptType     string `json:"script_type"`
	UserAsk        string `json:"user_ask"`
	AsrText        string `json:"asr_text"`
}

type CozeWorkflowExecuteStatusLog struct {
	UserUUID      string `json:"user_uuid"`
	AssetsUUID    string `json:"assets_uuid"`
	UpdateTime    int64  `json:"update_time"`
	ConnectorUID  string `json:"connector_uid"`
	Logid         string `json:"logid"`
	ErrorCode     string `json:"error_code"`
	CreateTime    int64  `json:"create_time"`
	Token         string `json:"token"`
	RunMode       int    `json:"run_mode"`
	DebugURL      string `json:"debug_url"`
	ErrorMessage  string `json:"error_message"`
	Output        string `json:"output"`
	Cost          string `json:"cost"`
	ExecuteID     string `json:"execute_id"`
	BotID         string `json:"bot_id"`
	ConnectorID   string `json:"connector_id"`
	ExecuteStatus string `json:"execute_status"`
}
