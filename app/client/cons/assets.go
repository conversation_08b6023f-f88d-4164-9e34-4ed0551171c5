package cons

import "github.com/orsinium-labs/enum"

type Source enum.Member[string]

var (
	Manual     = Source{"MANUAL"}
	Collection = Source{"COLLECTION"}
	Sources    = enum.New(Manual, Collection)
)

type HandleType enum.Member[string]

var (
	ANALYSIS                  = HandleType{"ANALYSIS"}
	GENERATION                = HandleType{"GENERATION"}
	TOPIC_SCRIPT_GENERATION   = HandleType{"TOPIC_SCRIPT_GENERATION"}
	TOPIC_SCRIPT_REGENERATION = HandleType{"TOPIC_SCRIPT_REGENERATION"}
	HandleTypes               = enum.New(ANALYSIS, GENERATION, TOPIC_SCRIPT_GENERATION, TOPIC_SCRIPT_REGENERATION)
)

type FlowStep enum.Member[string]

// 处理阶段
var (
	//  检测视频信息
	FlowStepCheckExtractInfo = FlowStep{"CHECK_EXTRACT_INFO"}
	//  上传视频
	FlowStepUploadVideo = FlowStep{"UPLOAD_VIDEO"}
	//  上传视频回调
	FlowStepUploadVideoCallback = FlowStep{"UPLOAD_VIDEO_CALLBACK"}
	//  解析视频
	FlowStepStartVideoParse = FlowStep{"VIDEO_PARSE"}
	//  生成视频雪碧图
	FlowStepStartVideoSnapshots = FlowStep{"VIDEO_SNAPSHOTS"}
	//  视频处理（包含解析视频、生成视频雪碧图）
	FlowStepVideoProcess = FlowStep{"SUB_VIDEO_PROCESS"}
	//  视频处理回调
	FlowStepVideoProcessCallback = FlowStep{"VIDEO_PROCESS_CALLBACK"}
	// 复用提取数据
	ProcessReuseExtractData = FlowStep{"PROCESS_REUSE_EXTRACT_DATA"}
	//  扣子解析
	FlowStepStartCozeAnalysis = FlowStep{"COZE_ANALYSIS"}
	//  扣子解析回调
	FlowStepStartCozeAnalysisCallback = FlowStep{"COZE_ANALYSIS_CALLBACK"}
	//  扣子生成选题
	FlowStepStartCozeGeneration = FlowStep{"COZE_GENERATION"}
	//  扣子生成选题回调
	FlowStepStartCozeGenerationCallback = FlowStep{"COZE_GENERATION_CALLBACK"}
	FlowSteps                           = enum.New(FlowStepCheckExtractInfo, FlowStepUploadVideo, FlowStepStartVideoParse, FlowStepStartVideoSnapshots, FlowStepStartCozeAnalysis, FlowStepStartCozeGeneration)
)
