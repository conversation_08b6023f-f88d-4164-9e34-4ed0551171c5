package cons

import "github.com/orsinium-labs/enum"

type EinoExecuteSourceType enum.Member[string]

var (
	EinoExecuteAsset                     = EinoExecuteSourceType{"ASSET"}
	EinoExecuteAssetRegenerateTopics     = EinoExecuteSourceType{"ASSET_REGENERATE_TOPIC"}
	EinoExecuteTopicGenerateScriptCopy   = EinoExecuteSourceType{"TOPIC_GENERATE_SCRIPT_COPY"}
	EinoExecuteTopicRegenerateScriptCopy = EinoExecuteSourceType{"TOPIC_REGENERATE_SCRIPT_COPY"}
	EinoExecuteSourceTypes               = enum.New(EinoExecuteAsset, EinoExecuteAssetRegenerateTopics, EinoExecuteTopicGenerateScriptCopy, EinoExecuteTopicRegenerateScriptCopy)
)

type RelatedType enum.Member[string]

var (
	RelatedTypeTopic = RelatedType{"TOPIC"}
	RelatedTypeAsset = RelatedType{"ASSET"}
	RelatedTypes     = enum.New(RelatedTypeTopic, RelatedTypeAsset)
)
