package cons

import "github.com/orsinium-labs/enum"

type HandleStatus enum.Member[string]

var (
	None             = HandleStatus{"NONE"}
	Processing       = HandleStatus{"PROCESSING"}
	Preprocessing    = HandleStatus{"PREPROCESSING"}
	Pending          = HandleStatus{"PENDING"}
	Doing            = HandleStatus{"DOING"}
	Success          = HandleStatus{"SUCCESS"}
	Failed           = HandleStatus{"FAILED"}
	Error            = HandleStatus{"ERROR"}
	HandleStatusCons = enum.New(Pending, Doing, Success, Failed, Error)
)

type ApprovalStatus enum.Member[string]

var (
	ApprovalPending  = ApprovalStatus{"PENDING"}
	ApprovalApproved = ApprovalStatus{"APPROVED"}
	ApprovalRejected = ApprovalStatus{"REJECTED"}
	ApprovalStatuses = enum.New(ApprovalPending, ApprovalApproved, ApprovalRejected)
)


// ProcessStatus 处理状态常量（大写格式）
type ProcessStatus enum.Member[string]

var (
	ProcessPending = ProcessStatus{"PENDING"}
	ProcessSuccess = ProcessStatus{"SUCCESS"}
	ProcessFailed  = ProcessStatus{"FAILED"}
)

// AnalysisStatus 分析状态常量（大写格式）
type AnalysisStatus enum.Member[string]

var (
	AnalysisPending = AnalysisStatus{"PENDING"}
	AnalysisDoing   = AnalysisStatus{"DOING"}
	AnalysisSuccess = AnalysisStatus{"SUCCESS"}
	AnalysisFailed  = AnalysisStatus{"FAILED"}
)

// GenerationStatus 生成状态常量（大写格式）
type GenerationStatus enum.Member[string]

var (
	GenerationPending = GenerationStatus{"PENDING"}
	GenerationDoing   = GenerationStatus{"DOING"}
	GenerationSuccess = GenerationStatus{"SUCCESS"}
	GenerationFailed  = GenerationStatus{"FAILED"}
)

// SyncStatus 同步状态常量（大写格式）
type SyncStatus enum.Member[string]

var (
	SyncSuccess = SyncStatus{"SUCCESS"}
	SyncFailed  = SyncStatus{"FAILED"}
)
