package app

import (
	"gofly/utils/gf"
	"gofly/utils/tools/gctx"
	"gofly/utils/tools/gcfg"
)

type Config struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传["*"]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传["*"]
}

func init() {
	fpath := Config{NoNeedLogin: []string{"*"}, NoNeedAuths: []string{"*"}}
	gf.Register(&fpath, fpath)
}

// GetAppConfig 获取应用配置信息
// 返回各种应用配置参数
func (api *Config) GetAppConfig(c *gf.GinCtx) {
	ctx := gctx.New()
	
	// 获取微信相关配置
	// 微信公众号appId
	officialAppId, _ := gcfg.Instance().Get(ctx, "wechat.offiaccount.appId")
	officialAppIdStr := ""
	if officialAppId != nil {
		officialAppIdStr = officialAppId.String()
	}
	
	// 微信开放平台appId
	openPlatformAppId, _ := gcfg.Instance().Get(ctx, "wechat.openplatform.appId")
	openPlatformAppIdStr := ""
	if openPlatformAppId != nil {
		openPlatformAppIdStr = openPlatformAppId.String()
	}

	// 获取支付相关配置
	originalPrice, _ := gcfg.Instance().Get(ctx, "pay.display.paid.originalPrice")
	paymentPrice, _ := gcfg.Instance().Get(ctx, "pay.display.paid.paymentPrice")
	originalPriceStr := ""
	if originalPrice != nil {
		originalPriceStr = originalPrice.String()
	}
	paymentPriceStr := ""
	if paymentPrice != nil {
		paymentPriceStr = paymentPrice.String()
	}
	
	toolTypes, _ := gf.Redis().Do(ctx,"GET", "tool_types")
	toolTypesStr := ""
	if toolTypes != nil {
		toolTypesStr = toolTypes.String()
	}
	
	configData := gf.Map{
		"tools": gf.Map{
			"toolTypes": toolTypesStr,
		},
		"wechat": gf.Map{
			"offiaccount": gf.Map{
				"appid": officialAppIdStr,
			},
			"openplatform": gf.Map{
				"appid": openPlatformAppIdStr,
			},
		},
		"pay": gf.Map{
			"display": gf.Map{
				"paid": gf.Map{
					"originalPrice": originalPriceStr,
					"paymentPrice": paymentPriceStr,
				},
			},
		},
		// 其他应用配置可在此处添加
	}
	
	gf.Success().SetMsg("获取应用配置成功").SetData(configData).Regin(c)
} 