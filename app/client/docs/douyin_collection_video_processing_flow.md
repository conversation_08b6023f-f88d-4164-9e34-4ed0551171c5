# 抖音收藏夹视频处理流程

## 概述

本文档详细描述了抖音收藏夹视频的处理流程，包括从获取收藏夹视频到创建关联记录的完整过程。

## 处理流程

### 1. 查询收藏夹内视频

从用户的抖音收藏夹中获取视频列表。主要通过调用 MediaCrawler 服务的 `SyncSingleCollectionWithCookies` 接口实现:

```go
resp, httpResp, err := apiClient.APIAPI.SyncSingleCollectionWithCookies(ctx).
    Cookies(user.DouyinCookie).
    SingleCollectionSyncRequest(*syncRequest).
    Execute()
```

该接口会:
- 同步收藏夹信息到 `douyin_collects` 表
- 同步视频数据到 `douyin_aweme` 表
- 在 `trendinsight_video_related` 表中创建关联记录

### 2. 检查视频在 douyin_aweme 表是否存在

接口 `SyncSingleCollectionWithCookies` 会自动处理视频数据的同步，确保视频信息存在于 `douyin_aweme` 表中。如果视频已存在则更新，不存在则新增。

```go
var dbAwemeList []entity.DouyinAweme
err = setting.CrawlerModel(entity.DouyinAweme{}).
    Where("aweme_id", awemeIds).
    Scan(&dbAwemeList)
```

### 3. 不存在则新增，存在则更新

此步骤由 `SyncSingleCollectionWithCookies` 接口自动完成，不需要额外处理。

### 4. 检查视频在 user_inbox_video_related 是否创建了关联

需要检查视频是否已在 `user_inbox_video_related` 表中创建关联记录。这个表用于跟踪用户收件箱与视频的关联关系。

通过以下服务方法实现检查和创建关联:

```go
videoRelatedService := services.NewUserInboxVideoRelatedService()
videoRelatedService.BatchCreateFromCollectSync(user.UUID, sourceId, awemeIds)
```

### 5. 没有则创建关联

如果视频未在 `user_inbox_video_related` 表中创建关联，则通过 `BatchCreateFromCollectSync` 方法创建关联记录。

## 核心函数

### handleSyncCollectsVideos

处理收藏夹视频同步的核心函数:

```go
func handleSyncCollectsVideos(collect entity.DouyinCollect, user entity.User) []*entity.DouyinVideoInfo
```

该函数:
1. 创建 MediaCrawler 客户端
2. 调用 `SyncSingleCollectionWithCookies` 接口
3. 解析返回的 aweme_id 列表
4. 从数据库查询视频详细信息
5. 转换为 DouyinVideoInfo 格式并返回

### HandleSingleUserSyncCollection

处理用户所有符合条件的收藏夹视频同步:

```go
func HandleSingleUserSyncCollection(user entity.User) (int, []string, []string)
```

该函数:
1. 获取用户所有包含"素材"关键字的收藏夹
2. 遍历每个收藏夹并调用 `handleSyncCollectsVideos`
3. 检查哪些是新视频并创建关联记录
4. 返回总关联数、新视频ID列表和错误列表

### SyncCollects

API 接口函数，用于处理用户触发的收藏夹同步请求:

```go
func (api *Douyin) SyncCollects(c *gf.GinCtx)
```

该函数:
1. 获取用户信息和验证
2. 调用 `HandleSingleUserSyncCollection` 执行同步
3. 创建同步记录并关联 aweme_id 列表
4. 返回同步结果

## 数据表说明

### douyin_aweme 表

存储抖音视频的基本信息:
- aweme_id: 抖音视频ID
- title: 视频标题
- desc: 视频描述
- nickname: 作者昵称
- user_id: 作者ID
- cover_url: 封面图片URL
- video_download_url: 视频下载URL
- create_time: 视频创建时间
- liked_count: 点赞数
- comment_count: 评论数
- share_count: 分享数
- collected_count: 收藏数

### user_inbox_video_related 表

存储用户收件箱与视频的关联关系:
- user_uuid: 用户UUID
- source_id: 源ID（收藏夹ID）
- source_type: 源类型（collect）
- aweme_id: 抖音视频ID
- publish_time: 视频发布时间
- handle_status: 处理状态

## 错误处理

流程中包含完整的错误处理机制:
- MediaCrawler 客户端创建失败处理
- HTTP 请求错误处理
- 数据库查询错误处理
- 空数据处理

## 并发处理

使用 goroutine 处理资产工作流，避免阻塞主流程:

```go
go func() {
    for _, asset := range allAssetList {
        einoExecuteRecord, err := workflow.GetOrCreateEinoExecuteRecord(asset, cons.EinoExecuteAsset.Value)
        if err != nil {
            continue
        }

        workflow.HandleWorkflow(asset, &user, einoExecuteRecord)
        time.Sleep(time.Second * 1)
    }
}()
```

## 性能优化

1. 批量操作: 使用批量插入减少数据库访问次数
2. 超时控制: 设置 60 秒超时时间避免长时间等待
3. 数据去重: 自动跳过空的 aweme_id
4. 异步处理: 使用 goroutine 处理非关键路径任务