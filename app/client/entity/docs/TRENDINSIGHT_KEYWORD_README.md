# TrendInsight Keyword 实体类使用指南

## 📋 概述

`TrendInsightKeyword` 是根据 `trendinsight_keyword` 表结构创建的 Go 实体类，用于管理巨量算数平台的关键词数据。该实体类遵循项目的标准实现模式，使用 `setting.CrawlerModel` 进行数据库操作。

## 📁 文件结构

```
app/client/entity/
├── trendinsight_keyword.go          # 主实体类定义
├── trendinsight_keyword_example.go  # 使用示例和常用操作
├── trendinsight_keyword_test.go     # 单元测试
└── TRENDINSIGHT_KEYWORD_README.md   # 本文档
```

## 🏗️ 实体类结构

### 基础字段
- `ID` - 主键ID（自增）
- `CreatedAt` - 创建时间（精确到微秒）
- `UpdatedAt` - 更新时间（精确到微秒）

### 关键词信息字段
- `Keyword` - 关键词内容（必填，有索引）
- `KeywordHash` - 关键词哈希值（必填，唯一索引）

### 统计数据字段
- `HeatScore` - 热度评分（有索引）
- `SearchVolume` - 搜索量（有索引）
- `TrendDirection` - 趋势方向（rising/falling/stable/volatile）

### 分类和标签字段
- `Category` - 分类（有索引）
- `Tags` - 相关标签（JSON格式）

### 关联数据字段
- `VideoCount` - 相关视频数
- `AuthorCount` - 相关作者数

### 时间字段
- `Date` - 统计日期（有索引）
- `CrawlTime` - 爬取时间戳（有索引）

### 原始数据字段
- `RawData` - 原始数据（JSON格式）

## 🚀 使用示例

### 1. 创建关键词记录

```go
package main

import (
    "time"
    "gofly/app/client/entity"
)

func createKeyword() {
    keyword := &entity.TrendInsightKeyword{
        Keyword:        "人工智能",
        HeatScore:      85.5,
        SearchVolume:   100000,
        TrendDirection: "rising",
        Category:       "科技",
        Tags:           `["AI", "机器学习", "深度学习"]`,
        VideoCount:     500,
        AuthorCount:    100,
        Date:           time.Now().Truncate(24 * time.Hour),
        CrawlTime:      time.Now().Unix(),
        RawData:        `{"source": "trendinsight", "platform": "douyin"}`,
    }

    example := &entity.TrendInsightKeywordExample{}
    err := example.CreateKeyword(keyword)
    if err != nil {
        // 处理错误
    }
}
```

### 2. 查询关键词信息

```go
func getKeywords() {
    example := &entity.TrendInsightKeywordExample{}
    
    // 获取热门关键词
    hotKeywords, err := example.GetHotKeywords(10, 80.0)
    if err != nil {
        // 处理错误
    }
    
    // 获取趋势关键词
    trendingKeywords, err := example.GetTrendingKeywords("rising", 20)
    if err != nil {
        // 处理错误
    }
    
    // 根据分类获取关键词
    categoryKeywords, err := example.GetKeywordsByCategory("科技", 15)
    if err != nil {
        // 处理错误
    }
}
```

### 3. 搜索和分析

```go
func searchAndAnalyze() {
    example := &entity.TrendInsightKeywordExample{}
    
    // 搜索关键词
    keywords, err := example.SearchKeywords("人工智能")
    if err != nil {
        // 处理错误
    }
    
    // 获取关键词趋势数据
    trendData, err := example.GetKeywordTrend("人工智能", 30) // 最近30天
    if err != nil {
        // 处理错误
    }
    
    // 根据日期范围获取数据
    startDate := time.Now().AddDate(0, 0, -7)
    endDate := time.Now()
    weeklyData, err := example.GetKeywordsByDateRange(startDate, endDate)
    if err != nil {
        // 处理错误
    }
}
```

### 4. 更新统计数据

```go
func updateKeywordStats() {
    example := &entity.TrendInsightKeywordExample{}
    
    err := example.UpdateKeywordStats(
        "keyword_hash_123", 
        90.5,  // 新热度评分
        150000, // 新搜索量
        600,    // 新视频数
        120,    // 新作者数
    )
    if err != nil {
        // 处理错误
    }
}
```

## 🔧 特殊功能

### 关键词哈希生成

系统自动为每个关键词生成MD5哈希值，用于：
- 唯一标识关键词
- 快速查找和去重
- 数据库索引优化

```go
example := &entity.TrendInsightKeywordExample{}
hash := example.generateKeywordHash("人工智能") // 自动生成MD5哈希
```

### 趋势方向枚举

支持的趋势方向值：
- `"rising"` - 上升趋势
- `"falling"` - 下降趋势  
- `"stable"` - 稳定趋势
- `"volatile"` - 波动趋势

### JSON 数据字段

- `Tags` - 存储关键词相关标签数组
- `RawData` - 存储原始爬取数据

```json
// Tags 示例
["AI", "机器学习", "深度学习", "神经网络"]

// RawData 示例
{
  "source": "trendinsight",
  "platform": "douyin",
  "metrics": {
    "engagement": 0.85,
    "growth_rate": 0.15
  }
}
```

## 🔍 数据库索引

该实体类包含多个索引以优化查询性能：

- `keyword` - 关键词索引
- `keyword_hash` - 唯一哈希索引
- `heat_score` - 热度评分索引
- `search_volume` - 搜索量索引
- `category` - 分类索引
- `date` - 日期索引
- `crawl_time` - 爬取时间索引

## 🧪 测试

运行测试以验证实体类的正确性：

```bash
cd app/client/entity
go test -v -run TestTrendInsightKeyword
```

运行性能测试：

```bash
go test -bench=BenchmarkTrendInsightKeyword
go test -bench=BenchmarkKeywordHashGeneration
```

## 📝 注意事项

1. **必填字段**：`Keyword` 和 `KeywordHash` 是必填字段
2. **唯一约束**：`KeywordHash` 具有唯一索引
3. **自动哈希**：创建记录时会自动生成关键词哈希
4. **时间精度**：`CreatedAt` 和 `UpdatedAt` 精确到微秒
5. **JSON 格式**：`Tags` 和 `RawData` 必须是有效的JSON格式
6. **趋势方向**：使用预定义的枚举值
7. **数据库连接**：必须使用 `setting.CrawlerModel` 操作 `media_crawler` 数据库

## 🔄 与现有代码的集成

该实体类可以与其他 TrendInsight 实体类配合使用：
- 与 `TrendInsightAuthor` 关联分析作者关键词
- 与视频数据关联分析内容趋势
- 支持多维度数据分析和报表生成
