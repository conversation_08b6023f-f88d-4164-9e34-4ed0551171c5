# TrendInsight Keyword Video Relation 实体类使用指南

## 📋 概述

`TrendInsightKeywordVideoRelation` 是根据 `trendinsight_keyword_video_relation` 表结构创建的 Go 实体类，用于管理巨量算数平台关键词与视频之间的关联关系。该实体类遵循项目的标准实现模式，使用 `setting.CrawlerModel` 进行数据库操作。

## 📁 文件结构

```
app/client/entity/
├── trendinsight_keyword_video_relation.go          # 主实体类定义
├── trendinsight_keyword_video_relation_example.go  # 使用示例和常用操作
├── trendinsight_keyword_video_relation_test.go     # 单元测试
└── TRENDINSIGHT_KEYWORD_VIDEO_RELATION_README.md   # 本文档
```

## 🏗️ 实体类结构

### 基础字段
- `ID` - 主键ID（自增）
- `CreatedAt` - 创建时间（精确到微秒）
- `UpdatedAt` - 更新时间（精确到微秒）

### 关联字段
- `KeywordID` - 关键词ID（必填，外键，多重索引）
- `VideoID` - 视频ID（必填，多重索引）

### 关联属性字段
- `RelevanceScore` - 关联相关性评分（0.0-1.0，有索引）
- `AssociationType` - 关联类型（search/tag/content，有索引）
- `SearchRank` - 搜索结果中的排名（可选）
- `ClickCount` - 点击次数

### 时间戳字段
- `FirstAssociatedAt` - 首次关联时间戳（有索引）
- `LastUpdatedAt` - 最后更新时间戳（有索引）

### 扩展数据字段
- `ExtraData` - 扩展数据（JSON格式）

### 关联实体
- `Keyword` - 关联的关键词实体（可选，用于联表查询）

## 🚀 使用示例

### 1. 创建关联关系

```go
package main

import (
    "time"
    "gofly/app/client/entity"
)

func createRelation() {
    relation := &entity.TrendInsightKeywordVideoRelation{
        KeywordID:       1,
        VideoID:         "video_123456",
        RelevanceScore:  0.85,
        AssociationType: "search",
        SearchRank:      intPtr(5),
        ClickCount:      0,
        ExtraData:       `{"source": "search_result", "confidence": 0.9}`,
    }

    example := &entity.TrendInsightKeywordVideoRelationExample{}
    err := example.CreateRelation(relation)
    if err != nil {
        // 处理错误
    }
}

func intPtr(i int) *int {
    return &i
}
```

### 2. 查询关联关系

```go
func getRelations() {
    example := &entity.TrendInsightKeywordVideoRelationExample{}
    
    // 根据关键词ID获取关联的视频
    relations, err := example.GetRelationsByKeywordID(1, 10)
    if err != nil {
        // 处理错误
    }
    
    // 根据视频ID获取关联的关键词
    relations, err = example.GetRelationsByVideoID("video_123456")
    if err != nil {
        // 处理错误
    }
    
    // 获取高相关性的关联关系
    highRelevanceRelations, err := example.GetHighRelevanceRelations(0.8, 20)
    if err != nil {
        // 处理错误
    }
}
```

### 3. 更新关联数据

```go
func updateRelations() {
    example := &entity.TrendInsightKeywordVideoRelationExample{}
    
    // 更新相关性评分
    err := example.UpdateRelevanceScore(1, "video_123456", 0.9)
    if err != nil {
        // 处理错误
    }
    
    // 增加点击次数
    err = example.IncrementClickCount(1, "video_123456")
    if err != nil {
        // 处理错误
    }
}
```

### 4. 分析和统计

```go
func analyzeRelations() {
    example := &entity.TrendInsightKeywordVideoRelationExample{}
    
    // 获取关键词关联的热门视频
    topVideos, err := example.GetTopVideosByKeyword(1, 10)
    if err != nil {
        // 处理错误
    }
    
    // 获取统计信息
    stats, err := example.GetRelationStats()
    if err != nil {
        // 处理错误
    }
    
    // 根据时间范围获取关联关系
    startTime := time.Now().AddDate(0, 0, -7).Unix()
    endTime := time.Now().Unix()
    weeklyRelations, err := example.GetRelationsByDateRange(startTime, endTime)
    if err != nil {
        // 处理错误
    }
}
```

### 5. 批量操作

```go
func batchOperations() {
    example := &entity.TrendInsightKeywordVideoRelationExample{}
    
    // 批量创建关联关系
    relations := []*entity.TrendInsightKeywordVideoRelation{
        {
            KeywordID:       1,
            VideoID:         "video_001",
            RelevanceScore:  0.8,
            AssociationType: "search",
        },
        {
            KeywordID:       1,
            VideoID:         "video_002",
            RelevanceScore:  0.7,
            AssociationType: "tag",
        },
    }
    
    err := example.BatchCreateRelations(relations)
    if err != nil {
        // 处理错误
    }
}
```

## 🔧 特殊功能

### 关联类型枚举

支持的关联类型：
- `"search"` - 搜索关联（用户搜索关键词时出现的视频）
- `"tag"` - 标签关联（视频标签包含关键词）
- `"content"` - 内容关联（视频内容与关键词相关）
- `"recommendation"` - 推荐关联（算法推荐的关联）

### 相关性评分

- 范围：0.0 - 1.0
- 0.0：无关联
- 0.5：中等相关
- 1.0：高度相关

### 时间戳管理

系统自动管理时间戳：
- `FirstAssociatedAt` - 首次建立关联时设置
- `LastUpdatedAt` - 每次更新时自动更新

### JSON 扩展数据

`ExtraData` 字段可存储额外的关联信息：

```json
{
  "source": "search_result",
  "confidence": 0.9,
  "metadata": {
    "platform": "douyin",
    "algorithm_version": "v2.1"
  },
  "metrics": {
    "ctr": 0.15,
    "engagement_rate": 0.08
  }
}
```

## 🔍 数据库索引

该实体类包含多个索引以优化查询性能：

- `keyword_id` - 关键词ID索引
- `video_id` - 视频ID索引
- `keyword_id + video_id` - 复合唯一索引
- `relevance_score` - 相关性评分索引
- `association_type` - 关联类型索引
- `first_associated_at` - 首次关联时间索引
- `last_updated_at` - 最后更新时间索引

## 📊 统计功能

### 关联关系统计

```go
stats, err := example.GetRelationStats()
// 返回结果示例：
{
  "total_relations": 10000,
  "by_type": [
    {"association_type": "search", "count": 6000},
    {"association_type": "tag", "count": 3000},
    {"association_type": "content", "count": 1000}
  ],
  "avg_relevance_score": 0.75
}
```

### 关联数量统计

```go
// 获取特定关键词的关联视频数
keywordID := 1
count, err := example.GetRelationCount(&keywordID, nil)

// 获取特定视频的关联关键词数
videoID := "video_123"
count, err = example.GetRelationCount(nil, &videoID)

// 获取总关联数
count, err = example.GetRelationCount(nil, nil)
```

## 🧪 测试

运行测试以验证实体类的正确性：

```bash
cd app/client/entity
go test -v -run TestTrendInsightKeywordVideoRelation
```

运行性能测试：

```bash
go test -bench=BenchmarkTrendInsightKeywordVideoRelation
go test -bench=BenchmarkRelationCreation
```

## 📝 注意事项

1. **必填字段**：`KeywordID` 和 `VideoID` 是必填字段
2. **唯一约束**：`KeywordID + VideoID` 组合具有唯一约束
3. **外键关系**：`KeywordID` 应该对应 `trendinsight_keyword` 表中的有效记录
4. **相关性评分**：建议保持在 0.0-1.0 范围内
5. **时间戳自动管理**：创建和更新时会自动设置时间戳
6. **JSON 格式**：`ExtraData` 必须是有效的JSON格式
7. **数据库连接**：必须使用 `setting.CrawlerModel` 操作 `media_crawler` 数据库

## 🔄 与现有代码的集成

该实体类可以与其他 TrendInsight 实体类配合使用：
- 与 `TrendInsightKeyword` 关联查询关键词详情
- 与视频相关实体关联分析视频数据
- 支持多维度关联分析和推荐算法
- 可用于构建关键词-视频关系图谱
