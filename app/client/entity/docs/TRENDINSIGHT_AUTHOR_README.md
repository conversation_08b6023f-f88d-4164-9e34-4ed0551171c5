# TrendInsight Author 实体类使用指南

## 📋 概述

`TrendInsightAuthor` 是根据 `trendinsight_author` 表结构创建的 Go 实体类，用于管理趋势洞察平台的作者信息。该实体类遵循项目的标准实现模式，使用 `setting.CrawlerModel` 进行数据库操作。

## 📁 文件结构

```
app/client/entity/
├── trendinsight_author.go          # 主实体类定义
├── trendinsight_author_example.go  # 使用示例和常用操作
├── trendinsight_author_test.go     # 单元测试
└── TRENDINSIGHT_AUTHOR_README.md   # 本文档
```

## 🏗️ 实体类结构

### 基础字段
- `ID` - 自增主键
- `CreatedAt` - 创建时间
- `UpdatedAt` - 更新时间

### 作者信息字段
- `AuthorID` - 作者唯一标识（必填，唯一索引）
- `AuthorName` - 作者名称
- `Nickname` - 昵称
- `Avatar` - 头像地址
- `Bio` - 个人简介

### 认证相关字段
- `IsVerified` - 是否认证
- `VerificationType` - 认证类型
- `VerificationInfo` - 认证信息

### 统计数据字段
- `FollowersCount` - 粉丝数
- `FollowingCount` - 关注数
- `VideoCount` - 视频数
- `LikeCount` - 获赞数

### 平台相关字段
- `Platform` - 平台名称（必填）
- `PlatformAuthorID` - 平台作者ID

### 分类和标签字段
- `Category` - 分类
- `Tags` - 标签（JSON格式）

### 时间戳字段
- `LastPostTime` - 最后发布时间
- `RegisterTime` - 注册时间
- `CrawlTime` - 爬取时间

### 其他字段
- `ActivityScore` - 活跃度评分
- `SourceKeyword` - 搜索来源关键字
- `RawData` - 原始数据（JSON格式）
- `ExtraInfo` - 额外信息（JSON格式）

## 🚀 使用示例

### 1. 创建作者记录

```go
package main

import (
    "time"
    "gofly/app/client/entity"
)

func createAuthor() {
    author := &entity.TrendInsightAuthor{
        AuthorID:         "douyin_123456",
        AuthorName:       "测试作者",
        Nickname:         "测试昵称",
        Avatar:           "https://example.com/avatar.jpg",
        Bio:              "这是一个测试作者",
        IsVerified:       true,
        VerificationType: "个人认证",
        FollowersCount:   100000,
        VideoCount:       200,
        Platform:         "douyin",
        Category:         "科技",
        ActivityScore:    85.5,
        CrawlTime:        time.Now(),
    }

    example := &entity.TrendInsightAuthorExample{}
    err := example.CreateAuthor(author)
    if err != nil {
        // 处理错误
    }
}
```

### 2. 查询作者信息

```go
func getAuthor() {
    example := &entity.TrendInsightAuthorExample{}
    
    // 根据作者ID查询
    author, err := example.GetAuthorByID("douyin_123456")
    if err != nil {
        // 处理错误
    }
    
    // 根据平台查询作者列表
    authors, err := example.GetAuthorsByPlatform("douyin", 10)
    if err != nil {
        // 处理错误
    }
}
```

### 3. 更新作者统计数据

```go
func updateAuthorStats() {
    example := &entity.TrendInsightAuthorExample{}
    
    err := example.UpdateAuthorStats("douyin_123456", 150000, 250, 2000000)
    if err != nil {
        // 处理错误
    }
}
```

### 4. 搜索作者

```go
func searchAuthors() {
    example := &entity.TrendInsightAuthorExample{}
    
    // 关键词搜索
    authors, err := example.SearchAuthorsByKeyword("科技")
    if err != nil {
        // 处理错误
    }
    
    // 获取认证作者
    verifiedAuthors, err := example.GetVerifiedAuthors("douyin")
    if err != nil {
        // 处理错误
    }
}
```

## 🔧 数据库操作

### 使用 setting.CrawlerModel

所有数据库操作都通过 `setting.CrawlerModel` 进行，这确保了：
- 连接到正确的 `media_crawler` 数据库
- 遵循项目的数据库操作规范
- 支持事务和连接池管理

```go
import "gofly/setting"

// 基本查询
var author entity.TrendInsightAuthor
err := setting.CrawlerModel("trendinsight_author").
    Where("author_id", "douyin_123456").
    Scan(&author)

// 批量查询
var authors []*entity.TrendInsightAuthor
err := setting.CrawlerModel("trendinsight_author").
    Where("platform", "douyin").
    Order("followers_count DESC").
    Limit(10).
    Scan(&authors)

// 插入数据
_, err := setting.CrawlerModel("trendinsight_author").Insert(&author)

// 更新数据
_, err := setting.CrawlerModel("trendinsight_author").
    Where("author_id", "douyin_123456").
    Update("followers_count", 200000)
```

## 🧪 测试

运行测试以验证实体类的正确性：

```bash
cd app/client/entity
go test -v -run TestTrendInsightAuthor
```

运行性能测试：

```bash
go test -bench=BenchmarkTrendInsightAuthor
```

## 📝 注意事项

1. **必填字段**：`AuthorID` 和 `Platform` 是必填字段
2. **唯一约束**：`AuthorID` 具有唯一索引
3. **JSON 字段**：`Tags`、`RawData`、`ExtraInfo` 存储 JSON 格式数据
4. **时间字段**：使用 `time.Time` 类型，支持 NULL 值的字段使用指针类型
5. **数据库连接**：必须使用 `setting.CrawlerModel` 操作 `media_crawler` 数据库

## 🔄 与现有代码的集成

该实体类遵循项目现有的模式：
- 使用 `gmeta.Meta` 进行 ORM 表映射
- 遵循 GORM 标签规范
- 使用项目统一的命名约定
- 支持 JSON 序列化

可以与现有的 `DouyinAweme`、`Assets` 等实体类配合使用，构建完整的数据模型。
