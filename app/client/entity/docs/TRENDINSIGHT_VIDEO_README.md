# TrendInsight Video 实体类使用指南

## 📋 概述

`TrendInsightVideo` 是根据 `trendinsight_video` 表结构创建的 Go 实体类，用于管理巨量算数平台的视频信息。该实体类包含了丰富的视频元数据、统计信息和作者信息，遵循项目的标准实现模式，使用 `setting.CrawlerModel` 进行数据库操作。

## 📁 文件结构

```
app/client/entity/
├── trendinsight_video.go          # 主实体类定义
├── trendinsight_video_example.go  # 使用示例和常用操作
├── trendinsight_video_test.go     # 单元测试
└── TRENDINSIGHT_VIDEO_README.md   # 本文档
```

## 🏗️ 实体类结构

### 基础字段
- `ID` - 主键ID（自增）
- `CreatedAt` - 创建时间（精确到微秒）
- `UpdatedAt` - 更新时间（精确到微秒）

### 视频基本信息字段
- `VideoID` - 视频ID（必填，唯一索引）
- `Title` - 视频标题
- `Description` - 视频描述
- `VideoURL` - 视频链接
- `CoverURL` - 封面链接
- `Duration` - 视频时长（秒）

### 作者信息字段
- `AuthorID` - 作者ID（必填，有索引）
- `AuthorName` - 作者名称
- `AuthorAvatar` - 作者头像
- `AuthorFollowers` - 作者粉丝数

### 统计数据字段
- `PlayCount` - 播放量（bigint，有索引）
- `LikeCount` - 点赞数（有索引）
- `CommentCount` - 评论数
- `ShareCount` - 分享数
- `CollectCount` - 收藏数

### 分类和标签字段
- `Category` - 分类名称
- `CategoryID` - 分类ID（有索引）
- `Tags` - 标签列表（JSON格式）
- `Hashtags` - 话题标签（JSON格式）

### 平台相关字段
- `Platform` - 来源平台（有索引）
- `PlatformVideoID` - 平台原始视频ID

### 时间戳字段
- `PublishTime` - 发布时间戳（有索引）
- `CrawlTime` - 爬取时间戳（有索引）

### 其他字段
- `SourceKeyword` - 搜索来源关键字（有索引）
- `DataQualityScore` - 数据质量评分（0.0-1.0）
- `IsVerified` - 是否已验证
- `RawData` - 原始数据（JSON格式）
- `ExtraInfo` - 扩展信息（JSON格式）

### 关联实体
- `Author` - 关联的作者实体（可选，用于联表查询）

## 🚀 使用示例

### 1. 创建视频记录

```go
package main

import (
    "time"
    "gofly/app/client/entity"
)

func createVideo() {
    video := &entity.TrendInsightVideo{
        VideoID:           "video_123456789",
        Title:             "人工智能技术发展趋势",
        Description:       "详细介绍人工智能技术的最新发展趋势",
        VideoURL:          "https://example.com/video/123456789",
        CoverURL:          "https://example.com/cover/123456789.jpg",
        Duration:          300,
        AuthorID:          "author_001",
        AuthorName:        "科技博主",
        AuthorFollowers:   100000,
        PlayCount:         500000,
        LikeCount:         15000,
        CommentCount:      2000,
        Category:          "科技",
        CategoryID:        "1",
        Tags:              `["人工智能", "科技", "趋势分析"]`,
        Platform:          "douyin",
        PublishTime:       time.Now().AddDate(0, 0, -1).Unix(),
        SourceKeyword:     "人工智能",
        DataQualityScore:  0.95,
        IsVerified:        true,
    }

    example := &entity.TrendInsightVideoExample{}
    err := example.CreateVideo(video)
    if err != nil {
        // 处理错误
    }
}
```

### 2. 查询视频信息

```go
func getVideos() {
    example := &entity.TrendInsightVideoExample{}
    
    // 根据视频ID获取视频
    video, err := example.GetVideoByID("video_123456789")
    if err != nil {
        // 处理错误
    }
    
    // 根据作者获取视频列表
    authorVideos, err := example.GetVideosByAuthor("author_001", 10)
    if err != nil {
        // 处理错误
    }
    
    // 获取热门视频
    hotVideos, err := example.GetHotVideos(20, 100000) // 播放量大于10万
    if err != nil {
        // 处理错误
    }
    
    // 根据分类获取视频
    categoryVideos, err := example.GetVideosByCategory("1", 15)
    if err != nil {
        // 处理错误
    }
}
```

### 3. 搜索和筛选

```go
func searchAndFilter() {
    example := &entity.TrendInsightVideoExample{}
    
    // 搜索视频
    searchResults, err := example.SearchVideos("人工智能", 20)
    if err != nil {
        // 处理错误
    }
    
    // 根据平台获取视频
    platformVideos, err := example.GetVideosByPlatform("douyin", 30)
    if err != nil {
        // 处理错误
    }
    
    // 根据时长筛选
    shortVideos, err := example.GetVideosByDuration(0, 60, 25) // 1分钟以内
    if err != nil {
        // 处理错误
    }
    
    // 根据发布时间范围获取
    startTime := time.Now().AddDate(0, 0, -7).Unix() // 7天前
    endTime := time.Now().Unix()
    recentVideos, err := example.GetVideosByDateRange(startTime, endTime, 50)
    if err != nil {
        // 处理错误
    }
}
```

### 4. 更新视频数据

```go
func updateVideo() {
    example := &entity.TrendInsightVideoExample{}
    
    // 更新统计数据
    err := example.UpdateVideoStats(
        "video_123456789",
        600000, // 新播放量
        18000,  // 新点赞数
        2500,   // 新评论数
        1000,   // 新分享数
        1500,   // 新收藏数
    )
    if err != nil {
        // 处理错误
    }
    
    // 更新数据质量评分
    err = example.UpdateDataQualityScore("video_123456789", 0.98)
    if err != nil {
        // 处理错误
    }
    
    // 标记为已验证
    err = example.MarkAsVerified("video_123456789")
    if err != nil {
        // 处理错误
    }
}
```

### 5. 统计分析

```go
func analyzeVideos() {
    example := &entity.TrendInsightVideoExample{}
    
    // 获取统计信息
    stats, err := example.GetVideoStats()
    if err != nil {
        // 处理错误
    }
    
    // 获取已验证的视频
    verifiedVideos, err := example.GetVerifiedVideos(30)
    if err != nil {
        // 处理错误
    }
    
    // 根据来源关键词获取视频
    keywordVideos, err := example.GetVideosBySourceKeyword("人工智能", 40)
    if err != nil {
        // 处理错误
    }
}
```

## 🔧 特殊功能

### 平台支持

支持的主要平台：
- `"douyin"` - 抖音
- `"kuaishou"` - 快手
- `"xiaohongshu"` - 小红书
- `"bilibili"` - 哔哩哔哩
- `"weibo"` - 微博

### 数据质量评分

- 范围：0.0 - 1.0
- 0.0：数据质量很差
- 0.5：数据质量中等
- 1.0：数据质量优秀

### JSON 数据字段

#### Tags 标签示例
```json
["人工智能", "科技", "趋势分析", "机器学习"]
```

#### Hashtags 话题标签示例
```json
["#AI", "#科技前沿", "#未来趋势", "#技术分享"]
```

#### RawData 原始数据示例
```json
{
  "original_response": {...},
  "api_version": "v2.1",
  "crawl_method": "api",
  "data_source": "trendinsight"
}
```

#### ExtraInfo 扩展信息示例
```json
{
  "quality": "high",
  "source": "api",
  "processing_time": 1.23,
  "validation_status": "passed"
}
```

## 🔍 数据库索引

该实体类包含多个索引以优化查询性能：

- `video_id` - 视频ID唯一索引
- `author_id` - 作者ID索引
- `play_count` - 播放量索引
- `like_count` - 点赞数索引
- `category_id` - 分类ID索引
- `platform` - 平台索引
- `publish_time` - 发布时间索引
- `crawl_time` - 爬取时间索引
- `source_keyword` - 来源关键词索引

## 📊 统计功能

### 视频统计信息

```go
stats, err := example.GetVideoStats()
// 返回结果示例：
{
  "total_videos": 50000,
  "by_platform": [
    {"platform": "douyin", "count": 30000},
    {"platform": "kuaishou", "count": 15000},
    {"platform": "xiaohongshu", "count": 5000}
  ],
  "by_category": [
    {"category_id": "1", "category": "科技", "count": 10000},
    {"category_id": "2", "category": "娱乐", "count": 8000}
  ],
  "avg_play_count": 125000.5,
  "avg_duration": 180.2
}
```

### 视频数量统计

```go
// 获取特定平台的视频数
platform := "douyin"
count, err := example.GetVideoCount(&platform, nil)

// 获取特定分类的视频数
categoryID := "1"
count, err = example.GetVideoCount(nil, &categoryID)

// 获取总视频数
count, err = example.GetVideoCount(nil, nil)
```

## 🧪 测试

运行测试以验证实体类的正确性：

```bash
cd app/client/entity
go test -v -run TestTrendInsightVideo
```

运行性能测试：

```bash
go test -bench=BenchmarkTrendInsightVideo
```

## 📝 注意事项

1. **必填字段**：`VideoID` 和 `AuthorID` 是必填字段
2. **唯一约束**：`VideoID` 具有唯一索引
3. **数据类型**：`PlayCount` 使用 `int64` 类型支持大数值
4. **时间戳自动管理**：创建时会自动设置爬取时间
5. **JSON 格式**：`Tags`、`Hashtags`、`RawData`、`ExtraInfo` 必须是有效的JSON格式
6. **数据质量评分**：建议保持在 0.0-1.0 范围内
7. **外键关系**：`AuthorID` 应该对应 `trendinsight_author` 表中的有效记录
8. **数据库连接**：必须使用 `setting.CrawlerModel` 操作 `media_crawler` 数据库

## 🔄 与现有代码的集成

该实体类可以与其他 TrendInsight 实体类配合使用：
- 与 `TrendInsightAuthor` 关联查询作者详情
- 与 `TrendInsightKeyword` 通过关联表建立关系
- 与 `TrendInsightKeywordVideoRelation` 配合进行关联分析
- 支持多维度数据分析和内容推荐算法
