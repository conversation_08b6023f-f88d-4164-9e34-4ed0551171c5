# UserCrawlerKeyword 迁移到 BaseEntity 架构

## 概述

本文档描述了将 `UserCrawlerKeyword` 结构体从自定义字段改为继承 `BaseEntity` 基础结构，并将关联字段从 `keyword_hash` 改为使用 `TrendInsightKeyword` 表的 `id` 字段进行关联的迁移过程。

## 主要变更

### 1. 实体类结构变更

#### UserCrawlerKeyword 结构体

**变更前：**
```go
type UserCrawlerKeyword struct {
    gmeta.Meta `orm:"table:user_crawler_keywords"`
    
    // 基础字段
    ID        int       `gorm:"column:id;type:int;primaryKey;autoIncrement;comment:主键ID" json:"id"`
    CreatedAt time.Time `gorm:"column:created_at;type:datetime(6);not null;default:CURRENT_TIMESTAMP(6);comment:创建时间" json:"created_at"`
    UpdatedAt time.Time `gorm:"column:updated_at;type:datetime(6);not null;default:CURRENT_TIMESTAMP(6);onUpdate:CURRENT_TIMESTAMP(6);comment:更新时间" json:"updated_at"`
    
    // 关联字段
    UserUUID    string `gorm:"column:user_uuid;type:varchar(32);not null;index:idx_user_keyword_user;comment:用户UUID" json:"user_uuid"`
    KeywordHash string `gorm:"column:keyword_hash;type:varchar(64);not null;index:idx_user_keyword_hash;comment:关键词哈希值" json:"keyword_hash"`
}
```

**变更后：**
```go
type UserCrawlerKeyword struct {
    gmeta.Meta `orm:"table:user_crawler_keywords"`
    BaseEntity
    
    // 关联字段
    UserUUID  string `gorm:"column:user_uuid;type:varchar(32);not null;index:idx_user_keyword_user;comment:用户UUID" json:"user_uuid"`
    KeywordID int    `gorm:"column:keyword_id;type:int;not null;index:idx_user_keyword_id;comment:关键词ID" json:"keyword_id"`
}
```

#### CrawlerTask 结构体

**变更前：**
```go
// 关联字段
UserUUID    string `gorm:"column:user_uuid;type:varchar(32);not null;index:idx_task_user;comment:创建用户UUID" json:"user_uuid"`
KeywordHash string `gorm:"column:keyword_hash;type:varchar(64);not null;index:idx_task_keyword;comment:关键词哈希值" json:"keyword_hash"`
```

**变更后：**
```go
// 关联字段
UserUUID  string `gorm:"column:user_uuid;type:varchar(32);not null;index:idx_task_user;comment:创建用户UUID" json:"user_uuid"`
KeywordID int    `gorm:"column:keyword_id;type:int;not null;index:idx_task_keyword;comment:关键词ID" json:"keyword_id"`
```

### 2. 关联关系变更

#### UserCrawlerKeyword 关联关系

**变更前：**
```go
User    *User           `json:"user,omitempty" gorm:"foreignKey:UserUUID;references:UUID"`
Keyword *CrawlerKeyword `json:"keyword,omitempty" gorm:"foreignKey:KeywordHash;references:KeywordHash"`
```

**变更后：**
```go
User         *User                `json:"user,omitempty" gorm:"foreignKey:UserUUID;references:UUID"`
TrendKeyword *TrendInsightKeyword `json:"trend_keyword,omitempty" gorm:"foreignKey:KeywordID;references:ID"`
```

#### CrawlerTask 关联关系

**变更前：**
```go
User               *User               `json:"user,omitempty" gorm:"foreignKey:UserUUID;references:UUID"`
Keyword            *CrawlerKeyword     `json:"keyword,omitempty" gorm:"foreignKey:KeywordHash;references:KeywordHash"`
UserCrawlerKeyword *UserCrawlerKeyword `json:"user_crawler_keyword,omitempty" gorm:"foreignKey:UserUUID,KeywordHash;references:UserUUID,KeywordHash"`
```

**变更后：**
```go
User               *User               `json:"user,omitempty" gorm:"foreignKey:UserUUID;references:UUID"`
TrendKeyword       *TrendInsightKeyword `json:"trend_keyword,omitempty" gorm:"foreignKey:KeywordID;references:ID"`
UserCrawlerKeyword *UserCrawlerKeyword `json:"user_crawler_keyword,omitempty" gorm:"foreignKey:UserUUID,KeywordID;references:UserUUID,KeywordID"`
```

### 3. 服务层方法签名变更

#### UserCrawlerKeywordService

**变更前：**
```go
func (s *UserCrawlerKeywordService) GetByUserAndKeyword(userUUID, keywordHash string) (*entity.UserCrawlerKeyword, error)
func (s *UserCrawlerKeywordService) IncrementUsage(userUUID, keywordHash string) error
func (s *UserCrawlerKeywordService) IncrementTaskCount(userUUID, keywordHash string) error
```

**变更后：**
```go
func (s *UserCrawlerKeywordService) GetByUserAndKeyword(userUUID string, keywordID int) (*entity.UserCrawlerKeyword, error)
func (s *UserCrawlerKeywordService) IncrementUsage(userUUID string, keywordID int) error
func (s *UserCrawlerKeywordService) IncrementTaskCount(userUUID string, keywordID int) error
```

#### CrawlerKeywordService

**变更前：**
```go
func (s *CrawlerKeywordService) IncrementTask(keywordHash string) error
func (s *CrawlerKeywordService) UpdateSuccessRate(keywordHash string, isSuccess bool) error
```

**变更后：**
```go
func (s *CrawlerKeywordService) IncrementTask(keywordID int) error
func (s *CrawlerKeywordService) UpdateSuccessRate(keywordID int, isSuccess bool) error
```

### 4. 数据库表结构变更

#### user_crawler_keywords 表

**变更前：**
```sql
CREATE TABLE `user_crawler_keywords` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    `user_uuid` varchar(32) NOT NULL COMMENT '用户UUID',
    `keyword_hash` varchar(64) NOT NULL COMMENT '关键词哈希值',
    -- 其他字段...
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_keyword` (`user_uuid`, `keyword_hash`),
    CONSTRAINT `fk_user_crawler_keywords_keyword` FOREIGN KEY (`keyword_hash`) REFERENCES `crawler_keywords` (`keyword_hash`) ON DELETE CASCADE
);
```

**变更后：**
```sql
CREATE TABLE `user_crawler_keywords` (
    `uuid` varchar(32) NOT NULL COMMENT '主键UUID',
    `create_time` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `update_time` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    `user_uuid` varchar(32) NOT NULL COMMENT '用户UUID',
    `keyword_id` int NOT NULL COMMENT '关键词ID',
    -- 其他字段...
    PRIMARY KEY (`uuid`),
    UNIQUE KEY `uk_user_keyword` (`user_uuid`, `keyword_id`),
    CONSTRAINT `fk_user_crawler_keywords_keyword` FOREIGN KEY (`keyword_id`) REFERENCES `trendinsight_keyword` (`id`) ON DELETE CASCADE
);
```

#### crawler_tasks 表

**变更前：**
```sql
`keyword_hash` varchar(64) NOT NULL COMMENT '关键词哈希值',
KEY `idx_task_keyword` (`keyword_hash`),
CONSTRAINT `fk_crawler_tasks_keyword` FOREIGN KEY (`keyword_hash`) REFERENCES `crawler_keywords` (`keyword_hash`) ON DELETE CASCADE
```

**变更后：**
```sql
`keyword_id` int NOT NULL COMMENT '关键词ID',
KEY `idx_task_keyword` (`keyword_id`),
CONSTRAINT `fk_crawler_tasks_keyword` FOREIGN KEY (`keyword_id`) REFERENCES `trendinsight_keyword` (`id`) ON DELETE CASCADE
```

### 5. 查询语句变更

#### 联表查询

**变更前：**
```go
query := gf.Model("user_crawler_keywords uck").
    LeftJoin("crawler_keywords ck", "uck.keyword_hash = ck.keyword_hash").
    Fields("uck.*, ck.keyword, ck.category, ck.success_rate")
```

**变更后：**
```go
query := gf.Model("user_crawler_keywords uck").
    LeftJoin("trendinsight_keyword tk", "uck.keyword_id = tk.id").
    Fields("uck.*, tk.keyword, tk.category")
```

### 6. 存储过程和触发器变更

#### 存储过程

**变更前：**
```sql
CREATE PROCEDURE `UpdateKeywordStats`(IN keyword_hash_param VARCHAR(64))
BEGIN
    -- 使用 keyword_hash 进行查询和更新
    WHERE keyword_hash = keyword_hash_param
END
```

**变更后：**
```sql
CREATE PROCEDURE `UpdateKeywordStats`(IN keyword_id_param INT)
BEGIN
    -- 使用 keyword_id 进行查询和更新
    WHERE keyword_id = keyword_id_param
END
```

#### 触发器

**变更前：**
```sql
CREATE TRIGGER `tr_crawler_tasks_after_update` 
AFTER UPDATE ON `crawler_tasks`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        CALL UpdateKeywordStats(NEW.keyword_hash);
    END IF;
END
```

**变更后：**
```sql
CREATE TRIGGER `tr_crawler_tasks_after_update` 
AFTER UPDATE ON `crawler_tasks`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        CALL UpdateKeywordStats(NEW.keyword_id);
    END IF;
END
```

## 迁移优势

### 1. 性能提升
- **整数主键关联**：使用 `int` 类型的 `keyword_id` 替代 `varchar(64)` 的 `keyword_hash`，提高查询和联表性能
- **索引效率**：整数索引比字符串索引更高效
- **存储空间**：减少存储空间占用

### 2. 数据一致性
- **标准化主键**：使用 `BaseEntity` 提供的标准 UUID 主键
- **统一时间字段**：使用 `BaseEntity` 的标准时间字段命名
- **软删除支持**：继承 `BaseEntity` 的软删除功能

### 3. 代码维护性
- **统一架构**：与项目中其他实体类保持一致的架构
- **减少重复代码**：复用 `BaseEntity` 的通用字段和方法
- **类型安全**：使用整数 ID 避免字符串比较错误

### 4. 扩展性
- **外键约束**：更好的数据库约束支持
- **关联查询**：更高效的关联查询性能
- **数据迁移**：更容易进行数据迁移和维护

## 注意事项

### 1. 数据迁移
在应用这些变更前，需要进行数据迁移：
1. 备份现有数据
2. 创建新的表结构
3. 迁移现有数据到新结构
4. 验证数据完整性

### 2. 兼容性
- 确保所有相关的 API 接口保持兼容
- 更新相关的测试用例
- 验证前端应用的兼容性

### 3. 性能测试
- 对比迁移前后的查询性能
- 验证联表查询的效率提升
- 监控数据库负载变化

## 总结

通过将 `UserCrawlerKeyword` 迁移到 `BaseEntity` 架构并改用整数主键关联，我们实现了：

1. **架构统一**：与项目标准保持一致
2. **性能优化**：提高查询和关联性能
3. **维护简化**：减少重复代码和维护成本
4. **扩展性增强**：为未来功能扩展提供更好的基础

这次迁移为爬虫任务管理系统提供了更稳定、高效的数据架构基础。
