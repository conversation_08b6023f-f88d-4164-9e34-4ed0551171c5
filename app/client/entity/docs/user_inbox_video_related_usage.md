# 用户收件箱视频关联服务使用指南

## 概述

`UserInboxVideoRelated` 实体和相关服务用于管理用户收藏夹同步后的视频记录。当用户同步抖音收藏夹时，系统会将所有 `aweme_id` 批量创建到 `user_inbox_video_related` 表中。

## 核心服务

### 1. UserInboxVideoRelatedService

负责 `user_inbox_video_related` 表的基础 CRUD 操作。

### 2. CollectSyncService

负责收藏夹同步的业务逻辑，包括批量创建记录和处理待处理的视频。

## 使用示例

### 收藏夹同步

```go
package main

import (
    "fmt"
    "gofly/app/client/entity/services"
)

func syncUserCollects() {
    // 创建收藏夹同步服务
    syncService := services.NewCollectSyncService()
    
    // 模拟从抖音API获取的收藏视频ID列表
    awemeIds := []string{
        "7123456789012345678",
        "7123456789012345679",
        "7123456789012345680",
        // ... 更多视频ID
    }
    
    userUUID := "user_12345"
    collectSourceId := "collect_001" // 收藏夹ID或其他标识
    
    // 执行同步
    result, err := syncService.SyncUserCollects(userUUID, collectSourceId, awemeIds)
    if err != nil {
        fmt.Printf("同步失败: %v\n", err)
        return
    }
    
    // 输出同步结果
    fmt.Printf("同步完成:\n")
    fmt.Printf("  总数: %d\n", result.TotalAwemeIds)
    fmt.Printf("  新创建: %d\n", result.CreatedCount)
    fmt.Printf("  跳过重复: %d\n", result.SkippedCount)
    fmt.Printf("  耗时: %v\n", result.EndTime.Sub(result.StartTime))
}
```

### 处理待处理的视频

```go
func processPendingVideos() {
    syncService := services.NewCollectSyncService()
    
    userUUID := "user_12345"
    batchSize := 100 // 每批处理100条
    
    // 处理待处理的视频
    result, err := syncService.ProcessPendingCollects(userUUID, batchSize)
    if err != nil {
        fmt.Printf("处理失败: %v\n", err)
        return
    }
    
    fmt.Printf("处理完成:\n")
    fmt.Printf("  总记录数: %d\n", result.TotalRecords)
    fmt.Printf("  成功: %d\n", result.SuccessCount)
    fmt.Printf("  失败: %d\n", result.FailedCount)
}
```

### 查询用户视频记录

```go
func getUserVideos() {
    videoService := services.NewUserInboxVideoRelatedService()
    
    userUUID := "user_12345"
    status := "pending" // 查询待处理的视频
    page := 1
    pageSize := 20
    
    // 获取用户的视频记录
    records, total, err := videoService.GetUserVideosByStatus(userUUID, status, page, pageSize)
    if err != nil {
        fmt.Printf("查询失败: %v\n", err)
        return
    }
    
    fmt.Printf("找到 %d 条记录 (总共 %d 条)\n", len(records), total)
    for _, record := range records {
        fmt.Printf("  视频ID: %s, 状态: %s, 创建时间: %s\n", 
            record.AwemeId, record.HandleStatus, record.CreateTime.Format("2006-01-02 15:04:05"))
    }
}
```

### 更新视频处理状态

```go
func updateVideoStatus() {
    videoService := services.NewUserInboxVideoRelatedService()
    
    userUUID := "user_12345"
    awemeIds := []string{"7123456789012345678", "7123456789012345679"}
    
    // 批量标记为成功
    err := videoService.BatchUpdateHandleStatus(awemeIds, userUUID, "success")
    if err != nil {
        fmt.Printf("更新状态失败: %v\n", err)
        return
    }
    
    fmt.Println("状态更新成功")
}
```

### 获取统计信息

```go
func getVideoStats() {
    syncService := services.NewCollectSyncService()
    
    userUUID := "user_12345"
    
    // 获取统计信息
    stats, err := syncService.GetSyncStats(userUUID)
    if err != nil {
        fmt.Printf("获取统计失败: %v\n", err)
        return
    }
    
    fmt.Printf("用户视频统计:\n")
    fmt.Printf("  总视频数: %d\n", stats.TotalVideos)
    fmt.Printf("  状态统计:\n")
    for status, count := range stats.StatusStats {
        fmt.Printf("    %s: %d\n", status, count)
    }
    fmt.Printf("  类型统计:\n")
    for sourceType, count := range stats.TypeStats {
        fmt.Printf("    %s: %d\n", sourceType, count)
    }
}
```

## 数据库表结构

```sql
CREATE TABLE `user_inbox_video_related` (
    `uuid` VARCHAR(32) NOT NULL COMMENT '主键UUID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    
    `user_uuid` VARCHAR(32) NOT NULL COMMENT '用户UUID',
    `source_id` VARCHAR(32) NOT NULL COMMENT '源视频ID',
    
    `source_type` VARCHAR(10) NOT NULL DEFAULT 'video' COMMENT '爬取类型(video/author/collect)',
    `aweme_id` VARCHAR(32) NOT NULL COMMENT '抖音ID',
    `publish_time` DATETIME NOT NULL COMMENT '发布时间',
    `handle_status` VARCHAR(10) NOT NULL DEFAULT 'pending' COMMENT '处理状态(pending/success/failed)',
    
    PRIMARY KEY (`uuid`),
    
    INDEX `idx_user_keyword_user` (`user_uuid`),
    INDEX `idx_source_id` (`source_id`),
    INDEX `idx_aweme_id` (`aweme_id`),
    INDEX `idx_handle_status` (`handle_status`),
    INDEX `idx_source_type` (`source_type`),
    
    -- 复合索引
    INDEX `idx_user_status` (`user_uuid`, `handle_status`),
    INDEX `idx_user_source_type` (`user_uuid`, `source_type`),
    
    -- 唯一约束
    UNIQUE KEY `uk_user_aweme` (`user_uuid`, `aweme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收件箱视频关联表';
```

## 业务流程

### 1. 收藏夹同步流程

1. 用户触发收藏夹同步
2. 调用抖音API获取收藏视频列表
3. 提取 `aweme_id` 列表
4. 调用 `CollectSyncService.SyncUserCollects()` 批量创建记录
5. 系统检查重复记录，只创建新的视频记录
6. 所有记录初始状态为 `pending`

### 2. 视频处理流程

1. 后台任务定期调用 `ProcessPendingCollects()` 处理待处理的视频
2. 对每个 `pending` 状态的视频执行业务处理（如内容分析、推荐等）
3. 根据处理结果更新状态为 `success` 或 `failed`

### 3. 查询和统计

1. 用户可以查看自己的收藏视频记录
2. 系统提供各种统计信息（按状态、类型等）
3. 支持分页查询和条件过滤

## 注意事项

1. **重复检查**: 系统会自动检查重复的 `aweme_id`，避免重复创建记录
2. **批量操作**: 使用批量插入和更新操作提高性能
3. **状态管理**: 通过 `handle_status` 字段跟踪视频处理状态
4. **软删除**: 使用 `is_deleted` 字段实现软删除，保留数据历史
5. **索引优化**: 合理设计索引提高查询性能

## 扩展性

该设计支持未来扩展：

1. 可以添加更多的 `source_type` 类型
2. 可以扩展 `handle_status` 状态
3. 可以添加更多的业务字段
4. 支持不同平台的视频ID格式
