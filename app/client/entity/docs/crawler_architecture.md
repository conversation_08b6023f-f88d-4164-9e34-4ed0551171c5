# 爬虫任务数据架构文档

## 概述

本文档描述了新的爬虫任务数据架构，该架构将直接调用 mediacrawler 服务改为在当前服务中维护本地数据表来管理爬虫任务。

## 架构设计

### 核心组件

1. **实体类 (Entities)**
   - `CrawlerKeyword`: 爬虫关键词实体
   - `UserCrawlerKeyword`: 用户关键词关联实体
   - `CrawlerTask`: 爬虫任务实体

2. **服务层 (Services)**
   - `CrawlerKeywordService`: 关键词管理服务
   - `UserCrawlerKeywordService`: 用户关键词关联服务
   - `CrawlerTaskService`: 爬虫任务管理服务

3. **数据库表**
   - `crawler_keywords`: 关键词表
   - `user_crawler_keywords`: 用户关键词关联表
   - `crawler_tasks`: 爬虫任务表

## 数据表结构

### crawler_keywords 表

存储去重后的关键词信息，使用 MD5 hash 进行去重。

**主要字段：**
- `keyword`: 关键词内容
- `keyword_hash`: 关键词MD5哈希值（唯一索引）
- `usage_count`: 使用次数
- `task_count`: 关联任务数
- `success_rate`: 成功率
- `category`: 关键词分类

### user_crawler_keywords 表

管理用户与关键词的关联关系，支持多用户使用相同关键词。

**主要字段：**
- `user_uuid`: 用户UUID
- `keyword_hash`: 关键词哈希值
- `alias`: 用户自定义别名
- `priority`: 优先级(0-100)
- `is_active`: 是否启用
- `auto_crawl`: 是否自动爬取

### crawler_tasks 表

管理爬虫任务的完整生命周期。

**主要字段：**
- `task_id`: 任务唯一标识
- `user_uuid`: 创建用户UUID
- `keyword_hash`: 关键词哈希值
- `platform`: 爬取平台
- `status`: 任务状态
- `progress`: 任务进度(0-100)

## 核心功能

### 1. 关键词去重机制

```go
// 使用 MD5 hash 进行关键词去重
func GenerateKeywordHash(keyword string) string {
    hash := md5.Sum([]byte(keyword))
    return fmt.Sprintf("%x", hash)
}

// 创建或获取关键词
keyword, err := keywordService.CreateOrGetKeyword("人工智能", "technology", "AI相关内容")
```

### 2. 多用户关键词共享

```go
// 用户1添加关键词
userKeyword1, err := userKeywordService.AddKeywordForUser(
    "user1-uuid", 
    "人工智能", 
    "我的AI关键词", 
    "technology", 
    80,
)

// 用户2添加相同关键词（会关联到同一个关键词记录）
userKeyword2, err := userKeywordService.AddKeywordForUser(
    "user2-uuid", 
    "人工智能", 
    "AI技术", 
    "technology", 
    70,
)
```

### 3. 任务生命周期管理

```go
// 创建任务
config := map[string]interface{}{
    "sort_type":        "latest",
    "max_notes":        50,
    "enable_comments":  true,
    "priority":         80,
}

task, err := taskService.CreateTask(userUUID, "人工智能", "douyin", "video", config)

// 启动任务
err = taskService.StartTask(task.TaskID)

// 更新进度
err = taskService.UpdateProgress(task.TaskID, 50)

// 完成任务
err = taskService.CompleteTask(task.TaskID)
```

## API 接口变更

### 创建爬虫任务

**接口：** `POST /api/v1/trendinsight/crawler/tasks`

**变更：**
- 移除了对 mediacrawler 服务的直接调用
- 改为操作本地数据库
- 支持批量关键词创建任务
- 自动处理关键词去重和用户关联

### 获取任务列表

**接口：** `GET /api/v1/trendinsight/crawler/tasks`

**变更：**
- 从本地数据库查询用户任务
- 支持按平台、状态过滤
- 增加了权限验证（只能查看自己的任务）

### 任务控制

**接口：** `POST /api/v1/trendinsight/crawler/tasks/{task_id}/control`

**变更：**
- 支持更多操作类型：start, stop, pause, resume, cancel, retry
- 增加了任务所有权验证
- 实时更新任务状态

## 使用示例

### 基础使用

```go
package main

import (
    "gofly/app/client/entity/examples"
)

func main() {
    example := examples.NewCrawlerExample()
    
    // 运行基础示例
    example.RunBasicExample()
    
    // 运行高级示例
    example.RunAdvancedExample()
    
    // 运行错误处理示例
    example.RunErrorHandlingExample()
}
```

### 服务层使用

```go
// 初始化服务
taskService := services.NewCrawlerTaskService()
keywordService := services.NewCrawlerKeywordService()
userKeywordService := services.NewUserCrawlerKeywordService()

// 创建关键词
keyword, err := keywordService.CreateOrGetKeyword("机器学习", "technology", "ML相关")

// 为用户添加关键词
userKeyword, err := userKeywordService.AddKeywordForUser(
    userUUID, 
    "机器学习", 
    "ML技术", 
    "technology", 
    75,
)

// 创建任务
task, err := taskService.CreateTask(userUUID, "机器学习", "douyin", "video", nil)
```

## 测试

### 运行单元测试

```bash
# 运行所有测试
go test ./app/client/entity/tests/...

# 运行特定测试
go test ./app/client/entity/tests/ -run TestCrawlerKeyword

# 运行基准测试
go test ./app/client/entity/tests/ -bench=.
```

### 运行集成测试

```bash
# 运行集成测试（需要数据库连接）
go test ./app/client/entity/tests/ -run TestIntegrationTestSuite

# 跳过集成测试
go test ./app/client/entity/tests/ -short
```

### 运行示例

```bash
# 运行示例代码
go run app/client/entity/examples/crawler_example.go
```

## 数据库迁移

### 执行迁移

```sql
-- 执行迁移脚本
source sql/1.4.0_crawler_tasks.sql
```

### 验证迁移

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'crawler_%';

-- 检查索引
SHOW INDEX FROM crawler_keywords;
SHOW INDEX FROM user_crawler_keywords;
SHOW INDEX FROM crawler_tasks;
```

## 性能优化

### 索引策略

1. **关键词表**
   - 唯一索引：`keyword_hash`
   - 复合索引：`status + category`
   - 复合索引：`usage_count + success_rate`

2. **用户关键词关联表**
   - 唯一索引：`user_uuid + keyword_hash`
   - 复合索引：`user_uuid + status`
   - 复合索引：`is_active + priority`

3. **任务表**
   - 唯一索引：`task_id`
   - 复合索引：`user_uuid + status`
   - 复合索引：`platform + status`

### 查询优化

1. 使用存储过程更新统计数据
2. 创建视图简化常用查询
3. 合理使用分页避免大量数据查询

## 监控和维护

### 统计信息

```go
// 获取关键词统计
stats, err := keywordService.GetStats()

// 获取用户关键词统计
userStats, err := userKeywordService.GetUserKeywordStats(userUUID)

// 获取任务统计
taskStats, err := taskService.GetTaskStats(userUUID)
```

### 数据清理

```sql
-- 清理软删除的数据（定期执行）
DELETE FROM crawler_keywords WHERE is_deleted = 1 AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM user_crawler_keywords WHERE is_deleted = 1 AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM crawler_tasks WHERE is_deleted = 1 AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 故障排除

### 常见问题

1. **关键词重复创建**
   - 检查 `keyword_hash` 生成是否正确
   - 验证唯一索引是否生效

2. **任务状态不更新**
   - 检查触发器是否正常工作
   - 验证存储过程执行情况

3. **权限验证失败**
   - 确认用户UUID获取方式
   - 检查任务所有权验证逻辑

### 调试工具

```go
// 启用调试日志
import "log"

// 在服务方法中添加日志
log.Printf("创建任务: userUUID=%s, keyword=%s", userUUID, keyword)
```

## 版本历史

- **v1.4.0**: 初始版本，实现本地数据库架构
- 支持关键词去重机制
- 支持多用户关键词共享
- 支持完整的任务生命周期管理
