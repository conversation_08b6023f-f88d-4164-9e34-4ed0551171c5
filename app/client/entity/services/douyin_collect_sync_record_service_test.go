package services

import (
	"testing"

	"gofly/app/client/entity"
	"gofly/utils/gf"

	"github.com/stretchr/testify/assert"
)

// TestCreateSyncRecordWithAwemeIds 测试创建同步记录并关联 aweme_id
func TestCreateSyncRecordWithAwemeIds(t *testing.T) {
	service := NewDouyinCollectSyncRecordService()
	userUUID := "test-user-uuid-123"
	awemeIds := []string{"aweme-id-1", "aweme-id-2", "aweme-id-3"}

	// 测试创建同步记录并关联 aweme_id
	record, err := service.CreateSyncRecordWithAwemeIds(userUUID, 3, "success", awemeIds)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, record)
	assert.Equal(t, userUUID, record.UserUUID)
	assert.Equal(t, 3, record.SyncCount)
	assert.Equal(t, "success", record.SyncStatus)

	// 验证关联记录是否创建
	var relatedRecords []entity.UserDouyinCollectSyncRecordRelated
	err = gf.Model("user_douyin_collect_sync_batch_related").
		Where("sync_record_uuid", record.UUID).
		Where("user_uuid", userUUID).
		Scan(&relatedRecords)

	assert.NoError(t, err)
	assert.Equal(t, 3, len(relatedRecords))

	// 验证每个关联记录的内容
	expectedAwemeIds := map[string]bool{
		"aweme-id-1": false,
		"aweme-id-2": false,
		"aweme-id-3": false,
	}

	for _, relatedRecord := range relatedRecords {
		assert.Equal(t, userUUID, relatedRecord.UserUUID)
		assert.Equal(t, record.UUID, relatedRecord.SyncRecordUUID)
		assert.Contains(t, expectedAwemeIds, relatedRecord.AwemeID)
		expectedAwemeIds[relatedRecord.AwemeID] = true
	}

	// 确保所有的 aweme_id 都被记录
	for awemeId, found := range expectedAwemeIds {
		assert.True(t, found, "aweme_id %s 未被记录", awemeId)
	}

	// 清理测试数据
	gf.Model("user_douyin_collect_sync_batch_related").
		Where("sync_record_uuid", record.UUID).
		Delete()
	gf.Model("douyin_collect_sync_records").
		Where("uuid", record.UUID).
		Delete()
}

// TestCreateSyncRecordWithEmptyAwemeIds 测试空 aweme_id 列表的情况
func TestCreateSyncRecordWithEmptyAwemeIds(t *testing.T) {
	service := NewDouyinCollectSyncRecordService()
	userUUID := "test-user-uuid-456"

	// 测试空的 aweme_id 列表
	record, err := service.CreateSyncRecordWithAwemeIds(userUUID, 2, "success", []string{})

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, record)
	assert.Equal(t, userUUID, record.UserUUID)
	assert.Equal(t, 2, record.SyncCount)

	// 验证没有创建关联记录
	var relatedRecords []entity.UserDouyinCollectSyncRecordRelated
	err = gf.Model("user_douyin_collect_sync_batch_related").
		Where("sync_record_uuid", record.UUID).
		Scan(&relatedRecords)

	assert.NoError(t, err)
	assert.Equal(t, 0, len(relatedRecords))

	// 清理测试数据
	gf.Model("user_douyin_collect_sync_records").
		Where("uuid", record.UUID).
		Delete()
}

// TestCreateSyncRecordWithZeroCount 测试同步数量为0的情况
func TestCreateSyncRecordWithZeroCount(t *testing.T) {
	service := NewDouyinCollectSyncRecordService()
	userUUID := "test-user-uuid-789"
	awemeIds := []string{"aweme-id-1", "aweme-id-2"}

	// 测试同步数量为0的情况
	record, err := service.CreateSyncRecordWithAwemeIds(userUUID, 0, "success", awemeIds)

	// 验证结果：不应该创建任何记录
	assert.NoError(t, err)
	assert.Nil(t, record)
}

// TestCreateSyncRecordRelatedRecords 测试创建关联记录的方法
func TestCreateSyncRecordRelatedRecords(t *testing.T) {
	service := NewDouyinCollectSyncRecordService()
	userUUID := "test-user-uuid-related"
	syncRecordUUID := "test-sync-record-uuid"
	awemeIds := []string{"aweme-test-1", "aweme-test-2", "", "aweme-test-3"} // 包含空字符串

	// 测试创建关联记录
	err := service.createSyncRecordRelatedRecords(userUUID, syncRecordUUID, awemeIds)

	// 验证结果
	assert.NoError(t, err)

	// 验证关联记录是否正确创建（应该跳过空字符串）
	var relatedRecords []entity.UserDouyinCollectSyncRecordRelated
	err = gf.Model("user_douyin_collect_sync_batch_related").
		Where("sync_record_uuid", syncRecordUUID).
		Where("user_uuid", userUUID).
		Scan(&relatedRecords)

	assert.NoError(t, err)
	assert.Equal(t, 3, len(relatedRecords)) // 应该只创建3条记录，跳过空字符串

	// 清理测试数据
	gf.Model("user_douyin_collect_sync_batch_related").
		Where("sync_record_uuid", syncRecordUUID).
		Delete()
}
