package services

import (
	"fmt"
	"gofly/app/client/entity"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gctx"
)

// TrendInsightVideoRelatedService 巨量引擎关键词与视频关联关系服务
type TrendInsightVideoRelatedService struct{}

// NewTrendInsightVideoRelatedService 创建服务实例
func NewTrendInsightVideoRelatedService() *TrendInsightVideoRelatedService {
	return &TrendInsightVideoRelatedService{}
}

// Create 创建关联记录
func (s *TrendInsightVideoRelatedService) Create(relation *entity.TrendInsightVideoRelated) error {
	var gfctx = gctx.New()

	// 验证数据
	if err := relation.Validate(); err != nil {
		return err
	}

	// 插入记录
	_, err := setting.CrawlerModel("trendinsight_video_related").
		Insert(relation)
	if err != nil {
		gf.Log().Error(gfctx, "创建视频关联记录失败", gf.Map{
			"error":       err.Error(),
			"source_id":   relation.SourceID,
			"source_type": relation.GetSourceType(),
			"video_id":    relation.VideoID,
		})
		return fmt.Errorf("创建视频关联记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "创建视频关联记录成功", gf.Map{
		"source_id":   relation.SourceID,
		"source_type": relation.GetSourceType(),
		"video_id":    relation.VideoID,
	})

	return nil
}

// BatchCreate 批量创建关联记录
func (s *TrendInsightVideoRelatedService) BatchCreate(relations []*entity.TrendInsightVideoRelated) error {
	var gfctx = gctx.New()

	if len(relations) == 0 {
		return nil
	}

	// 验证所有数据
	for i, relation := range relations {
		if err := relation.Validate(); err != nil {
			return fmt.Errorf("第%d条记录验证失败: %w", i+1, err)
		}
	}

	// 批量插入
	_, err := setting.CrawlerModel("trendinsight_video_related").
		Insert(relations)
	if err != nil {
		gf.Log().Error(gfctx, "批量创建视频关联记录失败", gf.Map{
			"error": err.Error(),
			"count": len(relations),
		})
		return fmt.Errorf("批量创建视频关联记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "批量创建视频关联记录成功", gf.Map{
		"count": len(relations),
	})

	return nil
}

// GetBySourceAndVideo 根据来源ID和视频ID获取关联记录
func (s *TrendInsightVideoRelatedService) GetBySourceAndVideo(sourceID, videoID string) (*entity.TrendInsightVideoRelated, error) {
	var relation entity.TrendInsightVideoRelated

	err := setting.CrawlerModel("trendinsight_video_related").
		Where("source_id", sourceID).
		Where("video_id", videoID).
		Where("is_deleted", 0).
		Scan(&relation)
	if err != nil {
		return nil, fmt.Errorf("查询视频关联记录失败: %w", err)
	}

	return &relation, nil
}

// GetBySourceID 根据来源ID获取所有关联的视频
func (s *TrendInsightVideoRelatedService) GetBySourceID(sourceID string, page, pageSize int) ([]*entity.TrendInsightVideoRelated, int64, error) {
	var relations []*entity.TrendInsightVideoRelated

	// 查询总数
	total, err := setting.CrawlerModel("trendinsight_video_related").
		Where("source_id", sourceID).
		Where("is_deleted", 0).
		Count()
	if err != nil {
		return nil, 0, fmt.Errorf("查询视频关联记录总数失败: %w", err)
	}

	// 分页查询
	err = setting.CrawlerModel("trendinsight_video_related").
		Where("source_id", sourceID).
		Where("is_deleted", 0).
		Order("created_at DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&relations)
	if err != nil {
		return nil, 0, fmt.Errorf("查询视频关联记录失败: %w", err)
	}

	return relations, int64(total), nil
}

// GetByVideoID 根据视频ID获取所有关联的来源
func (s *TrendInsightVideoRelatedService) GetByVideoID(videoID string) ([]*entity.TrendInsightVideoRelated, error) {
	var relations []*entity.TrendInsightVideoRelated

	err := setting.CrawlerModel("trendinsight_video_related").
		Where("video_id", videoID).
		Where("is_deleted", 0).
		Order("created_at DESC").
		Scan(&relations)
	if err != nil {
		return nil, fmt.Errorf("查询视频关联记录失败: %w", err)
	}

	return relations, nil
}

// GetBySourceType 根据来源类型获取关联记录
func (s *TrendInsightVideoRelatedService) GetBySourceType(sourceType string, page, pageSize int) ([]*entity.TrendInsightVideoRelated, int64, error) {
	var relations []*entity.TrendInsightVideoRelated

	// 查询总数
	total, err := setting.CrawlerModel("trendinsight_video_related").
		Where("source_type", sourceType).
		Where("is_deleted", 0).
		Count()
	if err != nil {
		return nil, 0, fmt.Errorf("查询视频关联记录总数失败: %w", err)
	}

	// 分页查询
	err = setting.CrawlerModel("trendinsight_video_related").
		Where("source_type", sourceType).
		Where("is_deleted", 0).
		Order("created_at DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&relations)
	if err != nil {
		return nil, 0, fmt.Errorf("查询视频关联记录失败: %w", err)
	}

	return relations, int64(total), nil
}

// Delete 软删除关联记录
func (s *TrendInsightVideoRelatedService) Delete(id int) error {
	var gfctx = gctx.New()

	_, err := setting.CrawlerModel("trendinsight_video_related").
		Where("id", id).
		Update(gf.Map{
			"is_deleted": 1,
			"updated_at": "NOW()",
		})
	if err != nil {
		gf.Log().Error(gfctx, "软删除视频关联记录失败", gf.Map{
			"error": err.Error(),
			"id":    id,
		})
		return fmt.Errorf("软删除视频关联记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "软删除视频关联记录成功", gf.Map{
		"id": id,
	})

	return nil
}

// DeleteBySourceAndVideo 根据来源ID和视频ID软删除关联记录
func (s *TrendInsightVideoRelatedService) DeleteBySourceAndVideo(sourceID, videoID string) error {
	var gfctx = gctx.New()

	_, err := setting.CrawlerModel("trendinsight_video_related").
		Where("source_id", sourceID).
		Where("video_id", videoID).
		Update(gf.Map{
			"is_deleted": 1,
			"updated_at": "NOW()",
		})
	if err != nil {
		gf.Log().Error(gfctx, "软删除视频关联记录失败", gf.Map{
			"error":     err.Error(),
			"source_id": sourceID,
			"video_id":  videoID,
		})
		return fmt.Errorf("软删除视频关联记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "软删除视频关联记录成功", gf.Map{
		"source_id": sourceID,
		"video_id":  videoID,
	})

	return nil
}

// Exists 检查关联记录是否存在
func (s *TrendInsightVideoRelatedService) Exists(sourceID, videoID string) (bool, error) {
	count, err := setting.CrawlerModel("trendinsight_video_related").
		Where("source_id", sourceID).
		Where("video_id", videoID).
		Where("is_deleted", 0).
		Count()
	if err != nil {
		return false, fmt.Errorf("检查视频关联记录是否存在失败: %w", err)
	}

	return count > 0, nil
}

// GetStats 获取统计信息
func (s *TrendInsightVideoRelatedService) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总记录数
	total, err := setting.CrawlerModel("trendinsight_video_related").
		Where("is_deleted", 0).
		Count()
	if err != nil {
		return nil, fmt.Errorf("查询总记录数失败: %w", err)
	}
	stats["total"] = total

	// 按来源类型统计
	var typeStats []map[string]interface{}
	err = setting.CrawlerModel("trendinsight_video_related").
		Fields("source_type, COUNT(*) as count").
		Where("is_deleted", 0).
		Group("source_type").
		Scan(&typeStats)
	if err != nil {
		return nil, fmt.Errorf("查询来源类型统计失败: %w", err)
	}
	stats["type_stats"] = typeStats

	return stats, nil
}
