package services

import (
	"testing"
	"time"
)

// TestCrawlerKeywordService_createMediaCrawlerClient 测试创建MediaCrawler客户端
func TestCrawlerKeywordService_createMediaCrawlerClient(t *testing.T) {
	service := NewCrawlerKeywordService()

	// 测试创建客户端
	apiClient, err := service.createMediaCrawlerClient()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("创建MediaCrawler客户端失败: %v", err)
		return
	}

	if apiClient == nil {
		t.<PERSON><PERSON><PERSON>("创建的客户端为nil")
		return
	}

	// 检查配置
	config := apiClient.GetConfig()
	if config == nil {
		t.<PERSON>rror("客户端配置为nil")
		return
	}

	if config.UserAgent != "QihaoZhushou-MediaCrawler-Client/1.0.0" {
		t.<PERSON><PERSON><PERSON>("用户代理不正确，期望: QihaoZhushou-MediaCrawler-Client/1.0.0, 实际: %s", config.UserAgent)
	}

	if config.HTTPClient.Timeout != 30*time.Second {
		t.<PERSON><PERSON><PERSON>("超时时间不正确，期望: 30s, 实际: %v", config.HTTPClient.Timeout)
	}
}

// TestCrawlerKeywordService_CreateKeywordOnly 测试仅创建关键词
func TestCrawlerKeywordService_CreateKeywordOnly(t *testing.T) {
	_ = NewCrawlerKeywordService()

	// 这个测试需要真实的MediaCrawler服务运行，所以只验证参数构造
	keyword := "人工智能"
	description := "AI技术相关内容监控"
	userID := int64(1)

	// 测试参数验证
	if keyword == "" {
		t.Error("关键词不能为空")
	}

	if userID <= 0 {
		t.Error("用户ID必须大于0")
	}

	// 注意：新的API不支持直接创建关键词请求，这里只验证参数有效性
	t.Logf("关键词参数验证成功: keyword=%s, userID=%d, description=%s", keyword, userID, description)
}

// TestCrawlerKeywordService_GetKeywordsByUser 测试获取用户关键词列表参数
func TestCrawlerKeywordService_GetKeywordsByUser(t *testing.T) {
	service := NewCrawlerKeywordService()

	userID := int64(1)
	page := int32(1)
	pageSize := int32(10)

	// 测试参数验证
	if userID <= 0 {
		t.Error("用户ID必须大于0")
	}

	if page < 1 {
		t.Error("页码必须大于等于1")
	}

	if pageSize < 1 || pageSize > 100 {
		t.Error("每页大小必须在1-100之间")
	}

	// 验证服务实例
	if service == nil {
		t.Error("服务实例为nil")
	}
}

// BenchmarkCrawlerKeywordService_createMediaCrawlerClient 性能测试
func BenchmarkCrawlerKeywordService_createMediaCrawlerClient(b *testing.B) {
	service := NewCrawlerKeywordService()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.createMediaCrawlerClient()
		if err != nil {
			b.Errorf("创建客户端失败: %v", err)
		}
	}
}
