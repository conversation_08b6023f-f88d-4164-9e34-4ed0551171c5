package services

import (
	"fmt"
	"time"

	"gofly/app/client/cons"
	"gofly/app/client/entity"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gctx"
)

// DouyinCollectSyncRecordService 抖音收藏同步记录服务
type DouyinCollectSyncRecordService struct{}

// NewDouyinCollectSyncRecordService 创建抖音收藏同步记录服务实例
func NewDouyinCollectSyncRecordService() *DouyinCollectSyncRecordService {
	return &DouyinCollectSyncRecordService{}
}

// CreateSyncRecord 创建同步记录
// 只有当 syncCount > 0 时才会创建记录
func (s *DouyinCollectSyncRecordService) CreateSyncRecord(userUUID string, syncCount int, syncStatus string) (*entity.DouyinCollectSyncRecord, error) {
	return s.CreateSyncRecordWithAwemeIds(userUUID, syncCount, syncStatus, nil)
}

// CreateSyncRecordWithAwemeIds 创建同步记录并创建关联的 aweme_id 记录
// 只有当 syncCount > 0 时才会创建记录
func (s *DouyinCollectSyncRecordService) CreateSyncRecordWithAwemeIds(userUUID string, syncCount int, syncStatus string, awemeIds []string) (*entity.DouyinCollectSyncRecord, error) {
	// 如果同步数量为0，则不创建记录
	if syncCount <= 0 {
		return nil, nil
	}

	// 如果状态为空，默认为成功
	if syncStatus == "" {
		syncStatus = cons.SyncSuccess.Value
	}

	record := &entity.DouyinCollectSyncRecord{
		BaseEntity: entity.BaseEntity{
			UUID:       gf.GenerateUUID(),
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
			IsDeleted:  false,
		},
		UserUUID:   userUUID,
		SyncTime:   time.Now(),
		SyncCount:  syncCount,
		SyncStatus: syncStatus,
	}

	// 插入数据库
	_, err := gf.Model(entity.DouyinCollectSyncRecord{}).Insert(record)
	if err != nil {
		return nil, fmt.Errorf("创建同步记录失败: %w", err)
	}

	// 如果有 aweme_id 列表，创建关联记录
	if len(awemeIds) > 0 {
		err = s.createSyncRecordRelatedRecords(userUUID, record.UUID, awemeIds)
		if err != nil {
			// 关联记录创建失败时记录错误，但不影响主记录
			gf.Log().Error(gctx.New(), "创建同步记录关联失败", gf.Map{
				"sync_record_uuid": record.UUID,
				"user_uuid":        userUUID,
				"aweme_count":      len(awemeIds),
				"error":            err.Error(),
			})
		}
	}

	return record, nil
}

// createSyncRecordRelatedRecords 创建同步记录关联的 aweme_id 记录
func (s *DouyinCollectSyncRecordService) createSyncRecordRelatedRecords(userUUID, syncRecordUUID string, awemeIds []string) error {
	if len(awemeIds) == 0 {
		return nil
	}

	// 构建关联记录列表
	var relatedRecords []entity.UserDouyinCollectSyncRecordRelated
	for _, awemeId := range awemeIds {
		if awemeId == "" {
			continue // 跳过空的 aweme_id
		}

		relatedRecord := entity.UserDouyinCollectSyncRecordRelated{
			BaseEntity: entity.BaseEntity{
				UUID:       gf.GenerateUUID(),
				CreateTime: time.Now(),
				UpdateTime: time.Now(),
				IsDeleted:  false,
			},
			UserUUID:       userUUID,
			SyncRecordUUID: syncRecordUUID,
			AwemeID:        awemeId,
		}
		relatedRecords = append(relatedRecords, relatedRecord)
	}

	if len(relatedRecords) == 0 {
		return nil
	}

	// 批量插入关联记录
	_, err := gf.Model(entity.UserDouyinCollectSyncRecordRelated{}).Data(relatedRecords).Save()
	if err != nil {
		return fmt.Errorf("创建同步记录关联失败: %w", err)
	}

	return nil
}

// GetByID 根据ID获取同步记录
func (s *DouyinCollectSyncRecordService) GetByID(id string) (*entity.DouyinCollectSyncRecord, error) {
	var record entity.DouyinCollectSyncRecord
	err := gf.Model(entity.DouyinCollectSyncRecord{}).
		Where("uuid", id).
		Where("is_deleted", false).
		Scan(&record)

	if err != nil {
		return nil, fmt.Errorf("查询同步记录失败: %w", err)
	}

	if record.UUID == "" {
		return nil, nil
	}

	return &record, nil
}

// GetUserSyncRecords 获取用户的同步记录列表
func (s *DouyinCollectSyncRecordService) GetUserSyncRecords(userUUID string, page, pageSize int) ([]*entity.DouyinCollectSyncRecord, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 查询总数
	totalCount, err := gf.Model(entity.DouyinCollectSyncRecord{}).
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Count()
	total := int64(totalCount)

	if err != nil {
		return nil, 0, fmt.Errorf("查询同步记录总数失败: %w", err)
	}

	// 查询记录列表
	var records []*entity.DouyinCollectSyncRecord
	err = gf.Model(entity.DouyinCollectSyncRecord{}).
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Order("sync_time DESC").
		Limit(pageSize).
		Offset(offset).
		Scan(&records)

	if err != nil {
		return nil, 0, fmt.Errorf("查询同步记录列表失败: %w", err)
	}

	return records, total, nil
}

// GetSyncStats 获取同步统计信息
func (s *DouyinCollectSyncRecordService) GetSyncStats(userUUID string, days int) (*SyncStats, error) {
	if days <= 0 {
		days = 30
	}

	startTime := time.Now().AddDate(0, 0, -days)

	// 查询统计数据
	var stats struct {
		TotalRecords   int64 `json:"total_records"`
		TotalSyncCount int64 `json:"total_sync_count"`
		SuccessCount   int64 `json:"success_count"`
		FailedCount    int64 `json:"failed_count"`
	}

	err := gf.Model(entity.DouyinCollectSyncRecord{}).
		Fields("COUNT(*) as total_records, SUM(sync_count) as total_sync_count, "+
			"SUM(CASE WHEN sync_status = 'success' THEN 1 ELSE 0 END) as success_count, "+
			"SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed_count").
		Where("user_uuid", userUUID).
		Where("sync_time >= ?", startTime).
		Where("is_deleted", false).
		Scan(&stats)

	if err != nil {
		return nil, fmt.Errorf("查询同步统计失败: %w", err)
	}

	result := &SyncStats{
		Days:           days,
		TotalRecords:   stats.TotalRecords,
		TotalSyncCount: stats.TotalSyncCount,
		SuccessCount:   stats.SuccessCount,
		FailedCount:    stats.FailedCount,
	}

	// 计算成功率
	if stats.TotalRecords > 0 {
		result.SuccessRate = float64(stats.SuccessCount) / float64(stats.TotalRecords) * 100
	}

	return result, nil
}

// SyncStats 同步统计信息
type SyncStats struct {
	Days           int   `json:"days"`             // 统计天数
	TotalRecords   int64 `json:"total_records"`    // 总记录数
	TotalSyncCount int64 `json:"total_sync_count"` // 总同步数量
	SuccessCount   int64 `json:"success_count"`    // 成功次数
	FailedCount    int64 `json:"failed_count"`     // 失败次数

	SuccessRate float64 `json:"success_rate"` // 成功率
}

// DeleteRecord 软删除同步记录
func (s *DouyinCollectSyncRecordService) DeleteRecord(id string) error {
	_, err := gf.Model(entity.DouyinCollectSyncRecord{}).
		Where("uuid", id).
		Update(gf.Map{
			"is_deleted":  true,
			"update_time": time.Now(),
		})

	if err != nil {
		return fmt.Errorf("删除同步记录失败: %w", err)
	}

	return nil
}

// CleanOldRecords 清理旧记录（物理删除超过指定天数的记录）
func (s *DouyinCollectSyncRecordService) CleanOldRecords(days int) (int64, error) {
	if days <= 0 {
		return 0, fmt.Errorf("天数必须大于0")
	}

	cutoffTime := time.Now().AddDate(0, 0, -days)

	result, err := gf.Model(entity.DouyinCollectSyncRecord{}).
		Where("sync_time < ?", cutoffTime).
		Delete()

	if err != nil {
		return 0, fmt.Errorf("清理旧记录失败: %w", err)
	}

	affected, _ := result.RowsAffected()
	return affected, nil
}

// GetSyncRecordRelated 获取用户指定同步记录的关联视频列表
func (s *DouyinCollectSyncRecordService) GetSyncRecordRelated(userUUID, syncRecordUUID string, page, pageSize int) ([]entity.UserDouyinCollectSyncRecordRelated, int64, error) {
	var gfctx = gctx.New()

	// 参数验证
	if userUUID == "" || syncRecordUUID == "" {
		return nil, 0, fmt.Errorf("用户UUID和同步记录UUID不能为空")
	}

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 验证同步记录是否属于该用户
	var syncRecord entity.DouyinCollectSyncRecord
	err := gf.Model(entity.DouyinCollectSyncRecord{}).
		Where("uuid", syncRecordUUID).
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Scan(&syncRecord)
	if err != nil {
		gf.Log().Error(gfctx, "验证同步记录失败", gf.Map{
			"error":            err.Error(),
			"user_uuid":        userUUID,
			"sync_record_uuid": syncRecordUUID,
		})
		return nil, 0, fmt.Errorf("验证同步记录失败: %w", err)
	}

	if syncRecord.UUID == "" {
		return nil, 0, fmt.Errorf("指定的同步记录不存在或不属于当前用户")
	}

	// 获取总数
	var total int64
	totalCount, err := gf.Model(entity.UserDouyinCollectSyncRecordRelated{}).
		Where("user_uuid", userUUID).
		Where("sync_record_uuid", syncRecordUUID).
		Where("is_deleted", false).
		Count()
	if err != nil {
		gf.Log().Error(gfctx, "获取关联记录总数失败", gf.Map{
			"error":            err.Error(),
			"user_uuid":        userUUID,
			"sync_record_uuid": syncRecordUUID,
		})
		return nil, 0, fmt.Errorf("获取记录总数失败: %w", err)
	}
	total = int64(totalCount)

	// 分页查询关联记录
	var records []entity.UserDouyinCollectSyncRecordRelated
	err = gf.Model(entity.UserDouyinCollectSyncRecordRelated{}).
		Where("user_uuid", userUUID).
		Where("sync_record_uuid", syncRecordUUID).
		Where("is_deleted", false).
		Order("create_time DESC").
		Page(page, pageSize).
		Scan(&records)
	if err != nil {
		gf.Log().Error(gfctx, "查询关联记录失败", gf.Map{
			"error":            err.Error(),
			"user_uuid":        userUUID,
			"sync_record_uuid": syncRecordUUID,
			"page":             page,
			"page_size":        pageSize,
		})
		return nil, 0, fmt.Errorf("查询关联记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "查询同步记录关联成功", gf.Map{
		"user_uuid":        userUUID,
		"sync_record_uuid": syncRecordUUID,
		"page":             page,
		"page_size":        pageSize,
		"total":            total,
		"records_count":    len(records),
	})

	return records, total, nil
}

// GetUserSyncRecordRelated 获取用户所有同步记录的关联视频列表
func (s *DouyinCollectSyncRecordService) GetUserSyncRecordRelated(userUUID string, page, pageSize int) ([]entity.UserDouyinCollectSyncRecordRelated, int64, error) {
	var gfctx = gctx.New()

	// 参数验证
	if userUUID == "" {
		return nil, 0, fmt.Errorf("用户UUID不能为空")
	}

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 获取总数
	var total int64
	totalCount, err := gf.Model(entity.UserDouyinCollectSyncRecordRelated{}).
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Count()
	if err != nil {
		gf.Log().Error(gfctx, "获取用户关联记录总数失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
		})
		return nil, 0, fmt.Errorf("获取记录总数失败: %w", err)
	}
	total = int64(totalCount)

	// 分页查询关联记录
	var records []entity.UserDouyinCollectSyncRecordRelated
	err = gf.Model(entity.UserDouyinCollectSyncRecordRelated{}).
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Order("create_time DESC").
		Page(page, pageSize).
		Scan(&records)
	if err != nil {
		gf.Log().Error(gfctx, "查询用户关联记录失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
			"page":      page,
			"page_size": pageSize,
		})
		return nil, 0, fmt.Errorf("查询关联记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "查询用户同步记录关联成功", gf.Map{
		"user_uuid":     userUUID,
		"page":          page,
		"page_size":     pageSize,
		"total":         total,
		"records_count": len(records),
	})

	return records, total, nil
}

// GetSyncRecordRelatedWithVideo 获取指定同步记录的关联视频，包含抖音视频详细信息
func (s *DouyinCollectSyncRecordService) GetSyncRecordRelatedWithVideo(userUUID, syncRecordUUID string, page, pageSize int) ([]entity.UserDouyinCollectSyncRecordRelatedWithVideo, int64, error) {
	var gfctx = gctx.New()

	// 首先获取基本的关联记录
	records, total, err := s.GetSyncRecordRelated(userUUID, syncRecordUUID, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	if len(records) == 0 {
		return []entity.UserDouyinCollectSyncRecordRelatedWithVideo{}, total, nil
	}

	// 提取所有的 aweme_id
	awemeIDs := make([]string, len(records))
	for i, record := range records {
		awemeIDs[i] = record.AwemeID
	}

	// 从 media_crawler 数据库查询对应的抖音视频信息
	var douyinAwemes []entity.DouyinAweme
	err = setting.CrawlerModel(entity.DouyinAweme{}).
		Where("aweme_id", awemeIDs).
		Scan(&douyinAwemes)
	if err != nil {
		gf.Log().Error(gfctx, "查询抖音视频信息失败", gf.Map{
			"error":     err.Error(),
			"aweme_ids": awemeIDs,
		})
	}

	// 从 media_crawler 数据库查询对应的趋势洞察视频信息
	var trendVideos []entity.TrendInsightVideo
	err = setting.CrawlerModel(entity.TrendInsightVideo{}).
		WhereIn("id", awemeIDs).
		Scan(&trendVideos)
	if err != nil {
		gf.Log().Error(gfctx, "查询趋势洞察视频信息失败", gf.Map{
			"error":     err.Error(),
			"aweme_ids": awemeIDs,
		})
	}

	// 创建 aweme_id -> DouyinAweme 的映射
	awemeMap := make(map[string]*entity.DouyinAweme)
	for i := range douyinAwemes {
		awemeMap[douyinAwemes[i].AwemeID] = &douyinAwemes[i]
	}

	// 创建 id -> TrendInsightVideo 的映射
	trendVideoMap := make(map[string]*entity.TrendInsightVideo)
	for i := range trendVideos {
		trendVideoMap[trendVideos[i].ID] = &trendVideos[i]
	}

	// 构建最终结果
	result := make([]entity.UserDouyinCollectSyncRecordRelatedWithVideo, len(records))
	for i, record := range records {
		trendVideo := trendVideoMap[record.AwemeID]
		var trendScore *float64 = nil
		if trendVideo != nil {
			score := float64(int(trendVideo.TrendScore * trendVideo.TrendRadio))
			trendScore = &score
		}

		result[i] = entity.UserDouyinCollectSyncRecordRelatedWithVideo{
			UserDouyinCollectSyncRecordRelated: record,
			DouyinAweme:                        awemeMap[record.AwemeID],
			TrendScore:                         trendScore,
		}
	}

	gf.Log().Info(gfctx, "查询同步记录关联视频成功", gf.Map{
		"user_uuid":          userUUID,
		"sync_record_uuid":   syncRecordUUID,
		"page":               page,
		"page_size":          pageSize,
		"total":              total,
		"records_count":      len(records),
		"videos_found":       len(douyinAwemes),
		"trend_videos_found": len(trendVideos),
	})

	return result, total, nil
}

// GetUserSyncRecordRelatedWithVideo 获取用户所有同步记录的关联视频，包含抖音视频详细信息
func (s *DouyinCollectSyncRecordService) GetUserSyncRecordRelatedWithVideo(userUUID string, page, pageSize int) ([]entity.UserDouyinCollectSyncRecordRelatedWithVideo, int64, error) {
	var gfctx = gctx.New()

	// 首先获取基本的关联记录
	records, total, err := s.GetUserSyncRecordRelated(userUUID, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	if len(records) == 0 {
		return []entity.UserDouyinCollectSyncRecordRelatedWithVideo{}, total, nil
	}

	// 提取所有的 aweme_id
	awemeIDs := make([]string, len(records))
	for i, record := range records {
		awemeIDs[i] = record.AwemeID
	}

	// 从 media_crawler 数据库查询对应的抖音视频信息
	var douyinAwemes []entity.DouyinAweme
	err = setting.CrawlerModel(entity.DouyinAweme{}).
		Where("aweme_id", awemeIDs).
		Scan(&douyinAwemes)
	if err != nil {
		gf.Log().Error(gfctx, "查询抖音视频信息失败", gf.Map{
			"error":     err.Error(),
			"aweme_ids": awemeIDs,
		})
	}

	// 从 media_crawler 数据库查询对应的趋势洞察视频信息
	var trendVideos []entity.TrendInsightVideo
	err = setting.CrawlerModel(entity.TrendInsightVideo{}).
		WhereIn("id", awemeIDs).
		Scan(&trendVideos)
	if err != nil {
		gf.Log().Error(gfctx, "查询趋势洞察视频信息失败", gf.Map{
			"error":     err.Error(),
			"aweme_ids": awemeIDs,
		})
	}

	// 创建 aweme_id -> DouyinAweme 的映射
	awemeMap := make(map[string]*entity.DouyinAweme)
	for i := range douyinAwemes {
		awemeMap[douyinAwemes[i].AwemeID] = &douyinAwemes[i]
	}

	// 创建 id -> TrendInsightVideo 的映射
	trendVideoMap := make(map[string]*entity.TrendInsightVideo)
	for i := range trendVideos {
		trendVideoMap[trendVideos[i].ID] = &trendVideos[i]
	}

	// 构建最终结果
	result := make([]entity.UserDouyinCollectSyncRecordRelatedWithVideo, len(records))
	for i, record := range records {
		trendVideo := trendVideoMap[record.AwemeID]
		var trendScore *float64 = nil
		if trendVideo != nil {
			score := float64(int(trendVideo.TrendScore * trendVideo.TrendRadio))
			trendScore = &score
		}

		result[i] = entity.UserDouyinCollectSyncRecordRelatedWithVideo{
			UserDouyinCollectSyncRecordRelated: record,
			DouyinAweme:                        awemeMap[record.AwemeID],
			TrendScore:                         trendScore,
		}
	}

	gf.Log().Info(gfctx, "查询用户同步记录关联视频成功", gf.Map{
		"user_uuid":          userUUID,
		"page":               page,
		"page_size":          pageSize,
		"total":              total,
		"records_count":      len(records),
		"videos_found":       len(douyinAwemes),
		"trend_videos_found": len(trendVideos),
	})

	return result, total, nil
}
