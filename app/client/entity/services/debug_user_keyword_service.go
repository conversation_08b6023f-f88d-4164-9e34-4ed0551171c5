package services

import (
	"fmt"
	"gofly/app/client/entity"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gconv"
)

// DebugUserKeywordService 用于调试用户关键词关联问题的服务
type DebugUserKeywordService struct {
	*UserInboxSourceRelatedService
}

// NewDebugUserKeywordService 创建调试服务实例
func NewDebugUserKeywordService() *DebugUserKeywordService {
	return &DebugUserKeywordService{
		UserInboxSourceRelatedService: NewUserInboxSourceRelatedService(),
	}
}

// DebugGetUserKeywordsByType 调试版本的获取用户关键词方法，包含详细日志
func (s *DebugUserKeywordService) DebugGetUserKeywordsByType(userUUID, crawlerType string, page, pageSize int) ([]*entity.UserInboxSourceRelated, int64, error) {
	fmt.Printf("=== 调试获取用户关键词 ===\n")
	fmt.Printf("用户UUID: %s\n", userUUID)
	fmt.Printf("类型: %s\n", crawlerType)
	fmt.Printf("分页: page=%d, pageSize=%d\n", page, pageSize)

	// 第一步：查询用户关键词关联
	query := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_type", crawlerType).
		Where("deleted_at IS NULL")

	// 获取总数
	total, err := query.Count()
	if err != nil {
		fmt.Printf("❌ 查询总数失败: %v\n", err)
		return nil, 0, err
	}
	fmt.Printf("✅ 找到总记录数: %d\n", total)

	// 分页查询用户关键词关联
	var userKeywords []*entity.UserInboxSourceRelated
	err = query.
		Order("create_time DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&userKeywords)

	if err != nil {
		fmt.Printf("❌ 查询用户关键词关联失败: %v\n", err)
		return nil, 0, err
	}
	fmt.Printf("✅ 查询到用户关键词关联记录: %d 条\n", len(userKeywords))

	// 打印用户关键词关联详情
	for i, uk := range userKeywords {
		fmt.Printf("  [%d] UUID: %s, SourceId: %s, SourceType: %s\n", i+1, uk.UUID, uk.SourceId, uk.SourceType)
	}

	// 第二步：根据类型批量查询并填充关联信息
	if len(userKeywords) > 0 {
		keywordIDs := make([]string, len(userKeywords))
		for i, uk := range userKeywords {
			keywordIDs[i] = uk.SourceId
		}
		fmt.Printf("✅ 需要查询的ID列表: %v\n", keywordIDs)

		if crawlerType == "video" {
			// 查询关键词信息
			var trendKeywords []*entity.TrendInsightKeyword
			err = setting.CrawlerModel("trendinsight_keyword").
				Where("id IN (?)", keywordIDs).
				Scan(&trendKeywords)

			if err != nil {
				fmt.Printf("❌ 查询关键词信息失败: %v\n", err)
				return nil, 0, fmt.Errorf("查询关键词信息失败: %w", err)
			}
			fmt.Printf("✅ 从 media_crawler 数据库查询到关键词: %d 条\n", len(trendKeywords))

			// 打印关键词详情
			for i, keyword := range trendKeywords {
				fmt.Printf("  [%d] ID: %d, Keyword: %s, Hash: %s\n", i+1, keyword.ID, keyword.Keyword, keyword.KeywordHash)
			}

			// 创建关键词ID到关键词对象的映射
			keywordMap := make(map[string]*entity.TrendInsightKeyword)
			for _, keyword := range trendKeywords {
				keywordMap[gconv.String(keyword.ID)] = keyword
			}
			fmt.Printf("✅ 创建关键词映射，包含 %d 个关键词\n", len(keywordMap))

			// 填充关键词信息
			filledCount := 0
			for _, uk := range userKeywords {
				if keyword, exists := keywordMap[uk.SourceId]; exists {
					uk.TrendKeyword = keyword
					filledCount++
					fmt.Printf("  ✅ 为用户关键词 %s 填充了关键词信息: %s\n", uk.UUID, keyword.Keyword)
				} else {
					fmt.Printf("  ❌ 用户关键词 %s 的关键词ID %s 在 trendinsight_keyword 表中不存在\n", uk.UUID, uk.SourceId)
				}
			}
			fmt.Printf("✅ 成功填充关键词信息: %d/%d\n", filledCount, len(userKeywords))

		} else if crawlerType == "author" {
			// 查询作者信息
			var trendAuthors []*entity.TrendInsightAuthor
			err = setting.CrawlerModel("trendinsight_author").
				Where("id IN (?)", keywordIDs).
				Scan(&trendAuthors)

			if err != nil {
				fmt.Printf("❌ 查询作者信息失败: %v\n", err)
				return nil, 0, fmt.Errorf("查询作者信息失败: %w", err)
			}
			fmt.Printf("✅ 从 media_crawler 数据库查询到作者: %d 条\n", len(trendAuthors))

			// 打印作者详情
			for i, author := range trendAuthors {
				fmt.Printf("  [%d] ID: %d, UserID: %s, UserName: %s, AwemeID: %s\n", i+1, author.ID, author.UserID, author.UserName, author.AwemeID)
			}

			// 创建作者ID到作者对象的映射
			authorMap := make(map[string]*entity.TrendInsightAuthor)
			for _, author := range trendAuthors {
				authorMap[gconv.String(author.ID)] = author
			}
			fmt.Printf("✅ 创建作者映射，包含 %d 个作者\n", len(authorMap))

			// 填充作者信息
			filledCount := 0
			for _, uk := range userKeywords {
				if author, exists := authorMap[uk.SourceId]; exists {
					uk.TrendAuthor = author
					filledCount++
					fmt.Printf("  ✅ 为用户关键词 %s 填充了作者信息: %s\n", uk.UUID, author.UserName)
				} else {
					fmt.Printf("  ❌ 用户关键词 %s 的作者ID %s 在 trendinsight_author 表中不存在\n", uk.UUID, uk.SourceId)
				}
			}
			fmt.Printf("✅ 成功填充作者信息: %d/%d\n", filledCount, len(userKeywords))
		}
	} else {
		fmt.Printf("⚠️  没有找到用户关键词关联记录\n")
	}

	fmt.Printf("=== 调试完成 ===\n\n")
	return userKeywords, int64(total), nil
}

// DebugDatabaseConnections 调试数据库连接
func (s *DebugUserKeywordService) DebugDatabaseConnections() error {
	fmt.Printf("=== 调试数据库连接 ===\n")

	// 测试默认数据库连接
	fmt.Printf("测试默认数据库连接...\n")
	count1, err1 := gf.Model("user_inbox_source_related").Count()
	if err1 != nil {
		fmt.Printf("❌ 默认数据库连接失败: %v\n", err1)
		return err1
	}
	fmt.Printf("✅ 默认数据库连接正常，user_inbox_source_related 表有 %d 条记录\n", count1)

	// 测试 media_crawler 数据库连接
	fmt.Printf("测试 media_crawler 数据库连接...\n")
	count2, err2 := setting.CrawlerModel("trendinsight_keyword").Count()
	if err2 != nil {
		fmt.Printf("❌ media_crawler 数据库连接失败: %v\n", err2)
		return err2
	}
	fmt.Printf("✅ media_crawler 数据库连接正常，trendinsight_keyword 表有 %d 条记录\n", count2)

	fmt.Printf("=== 数据库连接调试完成 ===\n\n")
	return nil
}

// DebugSpecificUserKeywords 调试特定用户的关键词
func (s *DebugUserKeywordService) DebugSpecificUserKeywords(userUUID string) error {
	fmt.Printf("=== 调试特定用户关键词 ===\n")
	fmt.Printf("用户UUID: %s\n", userUUID)

	// 查询该用户的所有关键词关联
	var userKeywords []*entity.UserInboxSourceRelated
	err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("deleted_at IS NULL").
		Scan(&userKeywords)

	if err != nil {
		fmt.Printf("❌ 查询用户关键词失败: %v\n", err)
		return err
	}

	fmt.Printf("✅ 用户总共有 %d 个关键词关联\n", len(userKeywords))

	// 按类型分组统计
	typeCount := make(map[string]int)
	keywordIDs := make([]string, 0)
	for _, uk := range userKeywords {
		typeCount[string(uk.SourceType)]++
		keywordIDs = append(keywordIDs, uk.SourceId)
		fmt.Printf("  - UUID: %s, SourceId: %s, SourceType: %s, CreateTime: %v\n",
			uk.UUID, uk.SourceId, string(uk.SourceType), uk.CreateTime)
	}

	fmt.Printf("按类型统计:\n")
	for t, count := range typeCount {
		fmt.Printf("  - %s: %d 个\n", t, count)
	}

	// 检查这些关键词在 media_crawler 数据库中是否存在
	if len(keywordIDs) > 0 {
		fmt.Printf("检查关键词在 media_crawler 数据库中的存在情况...\n")
		var trendKeywords []*entity.TrendInsightKeyword
		err = setting.CrawlerModel("trendinsight_keyword").
			Where("id", keywordIDs).
			Scan(&trendKeywords)

		if err != nil {
			fmt.Printf("❌ 查询 media_crawler 数据库失败: %v\n", err)
			return err
		}

		fmt.Printf("✅ 在 media_crawler 数据库中找到 %d/%d 个关键词\n", len(trendKeywords), len(keywordIDs))

		// 创建存在的关键词ID集合
		existingIDs := make(map[string]bool)
		for _, tk := range trendKeywords {
			existingIDs[gconv.String(tk.ID)] = true
			fmt.Printf("  - ID: %d, Keyword: %s\n", tk.ID, tk.Keyword)
		}

		// 检查缺失的关键词
		fmt.Printf("检查缺失的关键词:\n")
		missingCount := 0
		for _, id := range keywordIDs {
			if !existingIDs[id] {
				fmt.Printf("  ❌ 关键词ID %s 在 media_crawler 数据库中不存在\n", id)
				missingCount++
			}
		}
		if missingCount == 0 {
			fmt.Printf("  ✅ 所有关键词都存在\n")
		}
	}

	fmt.Printf("=== 特定用户关键词调试完成 ===\n\n")
	return nil
}
