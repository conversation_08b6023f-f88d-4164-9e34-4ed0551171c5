package services

import (
	"fmt"
	"gofly/app/client/entity"
	"gofly/utils/gf"
	"gofly/utils/tools/gcfg"
	"gofly/utils/tools/gconv"
	"gofly/utils/tools/gctx"
	"net/http"
	"strings"
	"time"

	client "github.com/qihaozhushou/mediacrawler-client"
)

// CrawlerKeywordService 关键词服务
// 通过 MediaCrawler API 管理关键词，不直接操作数据库
type CrawlerKeywordService struct{}

// NewCrawlerKeywordService 创建关键词服务实例
func NewCrawlerKeywordService() *CrawlerKeywordService {
	return &CrawlerKeywordService{}
}

// createMediaCrawlerClient 创建 MediaCrawler 客户端
func (s *CrawlerKeywordService) createMediaCrawlerClient() (*client.APIClient, error) {
	var gfctx = gctx.New()
	baseUrl, err := gcfg.Instance().Get(gfctx, "mediaCrawler.default.baseUrl")
	if err != nil {
		return nil, fmt.Errorf("获取MediaCrawler配置失败: %w", err)
	}

	baseUrlStr := baseUrl.String()
	if baseUrlStr == "" {
		baseUrlStr = "http://localhost:8080" // 更新默认端口以匹配新的OpenAPI规范
	}

	// 创建 OpenAPI 客户端配置
	cfg := client.NewConfiguration()
	cfg.Host = strings.TrimPrefix(strings.TrimPrefix(baseUrlStr, "http://"), "https://")
	if strings.HasPrefix(baseUrlStr, "https://") {
		cfg.Scheme = "https"
	} else {
		cfg.Scheme = "http"
	}

	// 设置用户代理
	cfg.UserAgent = "QihaoZhushou-MediaCrawler-Client/1.0.0"

	// 设置超时
	cfg.HTTPClient = &http.Client{
		Timeout: 30 * time.Second,
	}

	apiClient := client.NewAPIClient(cfg)
	return apiClient, nil
}

// CreateOrGetKeyword 创建或获取关键词（通过MediaCrawler API）
// 注意：这个方法现在主要用于兼容性，实际的关键词管理由MediaCrawler服务负责
func (s *CrawlerKeywordService) CreateOrGetKeyword(keyword, category string) (*entity.TrendInsightKeyword, error) {
	// 生成关键词哈希用于本地标识
	newKeyword := &entity.TrendInsightKeyword{
		Keyword: keyword,
		// 注意：category 信息不再存储，因为数据库表中没有对应字段
		// 如果需要存储 category 信息，需要在数据库表中添加相应字段
	}
	newKeyword.GenerateKeywordHash()

	// 注意：实际的关键词创建会在创建爬虫任务时由MediaCrawler服务自动处理
	// 这里返回一个本地构造的关键词对象，主要用于兼容现有的业务逻辑
	return newKeyword, nil
}

// Create 创建关键词（已废弃，由MediaCrawler服务管理）
func (s *CrawlerKeywordService) Create(keyword *entity.TrendInsightKeyword) error {
	// 不再直接创建关键词，由MediaCrawler服务在创建任务时自动处理
	return fmt.Errorf("关键词创建应该通过MediaCrawler服务的任务创建接口")
}

// GetByHash 根据哈希值获取关键词（已废弃，由MediaCrawler服务管理）
func (s *CrawlerKeywordService) GetByHash(keywordHash string) (*entity.TrendInsightKeyword, error) {
	// 不再直接查询关键词，应该通过MediaCrawler服务的API
	return nil, fmt.Errorf("关键词查询应该通过MediaCrawler服务的API")
}

// GetByKeyword 根据关键词内容获取（已废弃）
func (s *CrawlerKeywordService) GetByKeyword(keywordText string) (*entity.TrendInsightKeyword, error) {
	// 不再直接查询关键词，应该通过MediaCrawler服务的API
	return nil, fmt.Errorf("关键词查询应该通过MediaCrawler服务的API")
}

// GetByID 根据ID获取关键词（已废弃）
func (s *CrawlerKeywordService) GetByID(id int) (*entity.TrendInsightKeyword, error) {
	// 不再直接查询关键词，应该通过MediaCrawler服务的API
	return nil, fmt.Errorf("关键词查询应该通过MediaCrawler服务的API")
}


// GetKeywordsByUser 通过MediaCrawler API获取用户的关键词列表
func (s *CrawlerKeywordService) GetKeywordsByUser(userID int64, page, pageSize int32) (*client.KeywordSyncResponse, error) {
	// 注意：由于 OpenAPI 客户端接口变更，暂时返回错误
	return nil, fmt.Errorf("GetKeywordsByUser 接口暂时不可用，OpenAPI 客户端需要更新")
}

// CreateKeywordOnly 仅创建关键词（不创建爬虫任务）
// 注意：新的 API 不支持单独创建关键词，关键词是通过创建爬虫任务时自动创建的
func (s *CrawlerKeywordService) CreateKeywordOnly(keyword, description string, userID int64) (*client.KeywordSyncResponse, error) {
	// 注意：由于 OpenAPI 客户端接口变更，暂时返回错误
	return nil, fmt.Errorf("CreateKeywordOnly 接口暂时不可用，OpenAPI 客户端需要更新")
}

// DeleteKeywordByID 通过MediaCrawler API删除关键词
// 注意：新的 API 不支持单独删除关键词，关键词是通过删除相关的爬虫任务来间接删除的
func (s *CrawlerKeywordService) DeleteKeywordByID(keywordID int64) error {
	// 新的 API 架构中，关键词不能单独删除
	// 需要通过删除相关的爬虫任务来实现
	return fmt.Errorf("新的API不支持单独删除关键词，请通过删除相关的爬虫任务来实现")
}

// Update 更新关键词（已废弃）
func (s *CrawlerKeywordService) Update(keyword *entity.TrendInsightKeyword) error {
	return fmt.Errorf("关键词更新应该通过MediaCrawler服务的API")
}

// Delete 删除关键词（已废弃）
func (s *CrawlerKeywordService) Delete(id int) error {
	return fmt.Errorf("关键词删除应该通过MediaCrawler服务的API")
}

// List 获取关键词列表（已废弃）
func (s *CrawlerKeywordService) List(page, pageSize int, category, trendDirection string) ([]*entity.TrendInsightKeyword, int64, error) {
	return nil, 0, fmt.Errorf("关键词列表查询应该通过MediaCrawler服务的API")
}

// GetPopularKeywords 获取热门关键词（已废弃）
func (s *CrawlerKeywordService) GetPopularKeywords(limit int) ([]*entity.TrendInsightKeyword, error) {
	return nil, fmt.Errorf("热门关键词查询应该通过MediaCrawler服务的API")
}

// GetByCategory 根据分类获取关键词（已废弃）
func (s *CrawlerKeywordService) GetByCategory(category string) ([]*entity.TrendInsightKeyword, error) {
	return nil, fmt.Errorf("分类关键词查询应该通过MediaCrawler服务的API")
}

// SearchKeywords 搜索关键词（已废弃）
func (s *CrawlerKeywordService) SearchKeywords(searchText string, limit int) ([]*entity.TrendInsightKeyword, error) {
	return nil, fmt.Errorf("关键词搜索应该通过MediaCrawler服务的API")
}

// UpdateStats 更新关键词统计信息（已废弃）
func (s *CrawlerKeywordService) UpdateStats(keywordHash string, heatScore float64, searchVolume, videoCount, authorCount int) error {
	return fmt.Errorf("关键词统计更新应该通过MediaCrawler服务的API")
}

// GetStats 获取关键词统计信息（已废弃）
func (s *CrawlerKeywordService) GetStats() (map[string]any, error) {
	return nil, fmt.Errorf("关键词统计查询应该通过MediaCrawler服务的API")
}

// BatchCreate 批量创建关键词（已废弃）
func (s *CrawlerKeywordService) BatchCreate(keywords []*entity.TrendInsightKeyword) error {
	return fmt.Errorf("批量创建关键词应该通过MediaCrawler服务的API")
}

// BatchUpdateTrend 批量更新趋势方向（已废弃）
func (s *CrawlerKeywordService) BatchUpdateTrend(ids []int, trendDirection string) error {
	return fmt.Errorf("批量更新趋势方向应该通过MediaCrawler服务的API")
}

// GetRecentlyUsed 获取最近使用的关键词
// 只返回用户关键词关联信息，不查询具体的关键词详情
func (s *CrawlerKeywordService) GetRecentlyUsed(userUUID string, limit int) ([]*entity.TrendInsightKeyword, error) {
	// 从默认数据库查询用户最近使用的关键词关联
	var userKeywords []*entity.UserInboxSourceRelated
	err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("is_deleted", 0).
		WhereNotNull("last_used_at").
		Order("last_used_at DESC").
		Limit(limit).
		Scan(&userKeywords)

	if err != nil {
		return nil, fmt.Errorf("查询用户关键词关联失败: %v", err)
	}

	if len(userKeywords) == 0 {
		return []*entity.TrendInsightKeyword{}, nil
	}

	// 构造简单的关键词对象，只包含基本信息
	var keywords []*entity.TrendInsightKeyword
	for _, uk := range userKeywords {
		keyword := &entity.TrendInsightKeyword{
			ID:      gconv.Int(uk.SourceId),
			Keyword: uk.GetDisplayName(), // 使用显示名称（别名或关键词文本）
		}
		keyword.GenerateKeywordHash()
		keywords = append(keywords, keyword)
	}

	return keywords, nil
}

// UpdateHeatScore 更新热度评分（已废弃）
func (s *CrawlerKeywordService) UpdateHeatScore(keywordHash string, heatScore float64) error {
	return fmt.Errorf("热度评分更新应该通过MediaCrawler服务的API")
}

// UpdateSearchVolume 更新搜索量（已废弃）
func (s *CrawlerKeywordService) UpdateSearchVolume(keywordID int, searchVolume int) error {
	return fmt.Errorf("搜索量更新应该通过MediaCrawler服务的API")
}

// UpdateTrendDirection 更新趋势方向（已废弃）
func (s *CrawlerKeywordService) UpdateTrendDirection(keywordID int, trendDirection string) error {
	return fmt.Errorf("趋势方向更新应该通过MediaCrawler服务的API")
}

// UpdateCrawlTime 更新爬取时间（已废弃）
func (s *CrawlerKeywordService) UpdateCrawlTime(keywordHash string) error {
	return fmt.Errorf("爬取时间更新应该通过MediaCrawler服务的API")
}
