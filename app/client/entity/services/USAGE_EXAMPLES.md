# CrawlerTaskService 使用示例

## 📋 重构后的使用方法

重构后的 `CrawlerTaskService` 基于 `user_crawler_keywords` 实体，提供了更安全、更合理的数据访问方式。

## 🔧 基本使用

### 1. 获取用户任务列表

```go
package main

import (
    "fmt"
    "gofly/app/client/entity/services"
)

func getUserTasks() {
    // 创建服务实例
    service := services.NewCrawlerTaskService()
    
    userUUID := "user-123-456-789"
    page := 1
    pageSize := 20
    status := ""     // 空字符串表示不过滤状态
    platform := ""   // 空字符串表示不过滤平台
    
    // 获取用户任务列表（基于用户关键词）
    tasks, total, err := service.GetUserTasks(userUUID, page, pageSize, status, platform)
    if err != nil {
        fmt.Printf("获取任务失败: %v\n", err)
        return
    }
    
    fmt.Printf("用户任务总数: %d\n", total)
    fmt.Printf("当前页任务数: %d\n", len(tasks))
    
    for _, task := range tasks {
        fmt.Printf("任务ID: %s, 状态: %s, 平台: %s\n", 
            task.TaskID, task.Status, task.Platform)
    }
}
```

### 2. 获取任务和关键词信息

```go
func getUserTasksWithKeywords() {
    service := services.NewCrawlerTaskService()
    
    userUUID := "user-123-456-789"
    
    // 同时获取任务和关键词信息
    tasks, keywords, total, err := service.GetUserTasksWithKeywordInfo(
        userUUID, 1, 20, "", "")
    if err != nil {
        fmt.Printf("获取任务和关键词失败: %v\n", err)
        return
    }
    
    fmt.Printf("任务总数: %d\n", total)
    fmt.Printf("关键词数: %d\n", len(keywords))
    
    // 创建关键词映射
    keywordMap := make(map[int]string)
    for _, keyword := range keywords {
        keywordMap[keyword.KeywordID] = keyword.GetDisplayName()
    }
    
    // 显示任务和对应的关键词
    for _, task := range tasks {
        keywordName := "未知关键词"
        if name, exists := keywordMap[task.KeywordID]; exists {
            keywordName = name
        }
        
        fmt.Printf("任务: %s, 关键词: %s, 状态: %s\n", 
            task.TaskID, keywordName, task.Status)
    }
}
```

### 3. 按指定关键词获取任务

```go
func getTasksByKeywords() {
    service := services.NewCrawlerTaskService()
    
    userUUID := "user-123-456-789"
    keywordIDs := []int{1, 2, 3} // 指定关键词ID
    
    // 获取指定关键词的任务（会验证用户权限）
    tasks, total, err := service.GetUserTasksByKeywords(
        userUUID, keywordIDs, 1, 20, "running", "douyin")
    if err != nil {
        fmt.Printf("获取指定关键词任务失败: %v\n", err)
        return
    }
    
    fmt.Printf("指定关键词的运行中任务数: %d\n", total)
    
    for _, task := range tasks {
        fmt.Printf("任务: %s, 关键词ID: %d, 进度: %.2f%%\n", 
            task.TaskID, task.KeywordID, task.Progress)
    }
}
```

### 4. 获取任务统计

```go
func getTaskStats() {
    service := services.NewCrawlerTaskService()
    
    userUUID := "user-123-456-789"
    
    // 获取基于用户关键词的任务统计
    stats, err := service.GetTaskStats(userUUID)
    if err != nil {
        fmt.Printf("获取统计失败: %v\n", err)
        return
    }
    
    fmt.Printf("总任务数: %v\n", stats["total_tasks"])
    
    // 状态统计
    if statusStats, ok := stats["status_stats"].([]map[string]interface{}); ok {
        fmt.Println("状态统计:")
        for _, stat := range statusStats {
            fmt.Printf("  %s: %v\n", stat["status"], stat["count"])
        }
    }
    
    // 平台统计
    if platformStats, ok := stats["platform_stats"].([]map[string]interface{}); ok {
        fmt.Println("平台统计:")
        for _, stat := range platformStats {
            fmt.Printf("  %s: %v\n", stat["platform"], stat["count"])
        }
    }
    
    // 关键词统计（新增）
    if keywordStats, ok := stats["keyword_stats"].([]map[string]interface{}); ok {
        fmt.Println("关键词统计:")
        for _, stat := range keywordStats {
            fmt.Printf("  关键词ID %v: %v 个任务\n", stat["keyword_id"], stat["count"])
        }
    }
}
```

## 🛡️ 安全特性示例

### 1. 权限验证

```go
func demonstratePermissionControl() {
    service := services.NewCrawlerTaskService()
    
    userUUID := "user-123-456-789"
    
    // 尝试访问不属于用户的关键词
    unauthorizedKeywordIDs := []int{999, 1000} // 假设这些关键词不属于该用户
    
    tasks, total, err := service.GetUserTasksByKeywords(
        userUUID, unauthorizedKeywordIDs, 1, 20, "", "")
    
    if err != nil {
        fmt.Printf("权限验证失败: %v\n", err)
        return
    }
    
    // 即使没有错误，也应该返回空结果（因为用户没有这些关键词的权限）
    fmt.Printf("无权限关键词的任务数: %d\n", total) // 应该是 0
}
```

### 2. 数据隔离

```go
func demonstrateDataIsolation() {
    service := services.NewCrawlerTaskService()
    
    user1UUID := "user-111-111-111"
    user2UUID := "user-222-222-222"
    
    // 用户1的任务
    tasks1, total1, _ := service.GetUserTasks(user1UUID, 1, 100, "", "")
    
    // 用户2的任务
    tasks2, total2, _ := service.GetUserTasks(user2UUID, 1, 100, "", "")
    
    fmt.Printf("用户1任务数: %d\n", total1)
    fmt.Printf("用户2任务数: %d\n", total2)
    
    // 验证数据隔离：两个用户的任务不应该有重叠
    taskIDs1 := make(map[string]bool)
    for _, task := range tasks1 {
        taskIDs1[task.TaskID] = true
    }
    
    overlap := 0
    for _, task := range tasks2 {
        if taskIDs1[task.TaskID] {
            overlap++
        }
    }
    
    fmt.Printf("任务重叠数: %d (应该为0)\n", overlap)
}
```

## 📊 前端集成示例

### 1. 任务列表页面

```go
// 控制器方法示例
func (c *TaskController) GetUserTaskList(ctx *gin.Context) {
    userUUID := c.GetUserUUID(ctx)
    page := c.GetIntParam(ctx, "page", 1)
    pageSize := c.GetIntParam(ctx, "page_size", 20)
    status := c.GetStringParam(ctx, "status", "")
    platform := c.GetStringParam(ctx, "platform", "")
    
    service := services.NewCrawlerTaskService()
    
    // 获取任务和关键词信息
    tasks, keywords, total, err := service.GetUserTasksWithKeywordInfo(
        userUUID, page, pageSize, status, platform)
    if err != nil {
        c.ErrorResponse(ctx, "获取任务列表失败", err)
        return
    }
    
    // 构造响应数据
    response := map[string]interface{}{
        "tasks":    tasks,
        "keywords": keywords,
        "total":    total,
        "page":     page,
        "page_size": pageSize,
    }
    
    c.SuccessResponse(ctx, "获取成功", response)
}
```

### 2. 统计仪表板

```go
func (c *DashboardController) GetTaskStats(ctx *gin.Context) {
    userUUID := c.GetUserUUID(ctx)
    
    service := services.NewCrawlerTaskService()
    stats, err := service.GetTaskStats(userUUID)
    if err != nil {
        c.ErrorResponse(ctx, "获取统计失败", err)
        return
    }
    
    c.SuccessResponse(ctx, "获取统计成功", stats)
}
```

## 🔄 迁移指南

### 从旧版本迁移

```go
// 旧版本代码
func oldGetUserTasks(userUUID string) {
    // 直接查询 crawler_tasks 表
    // 可能存在权限和数据一致性问题
}

// 新版本代码
func newGetUserTasks(userUUID string) {
    service := services.NewCrawlerTaskService()
    
    // 基于用户关键词的安全查询
    tasks, total, err := service.GetUserTasks(userUUID, 1, 20, "", "")
    if err != nil {
        // 处理错误
        return
    }
    
    // 使用任务数据
    // ...
}
```

## 📝 注意事项

### 1. 性能考虑
- 大量关键词时可能影响查询性能，建议添加缓存
- 考虑分页查询关键词列表
- 建议添加相关数据库索引

### 2. 错误处理
- 用户无关键词时返回空列表，不是错误
- 权限验证失败时过滤数据，不抛出异常
- 数据库查询失败时返回明确的错误信息

### 3. 扩展性
- 可以基于用户关键词的个性化配置进行排序
- 支持按关键词分类、优先级等维度查询
- 可以实现关键词级别的任务统计

---

**重构优势**: ✅ 更安全、更合理、更可扩展的数据访问方式
