package services

import (
	"fmt"
	"gofly/app/client/entity"
	"time"
)

// CollectSyncService 收藏夹同步服务
// 处理用户收藏夹同步逻辑，将 aweme_id 批量创建到 user_inbox_video_related 表
type CollectSyncService struct {
	videoService *UserInboxVideoRelatedService
}

// NewCollectSyncService 创建收藏夹同步服务实例
func NewCollectSyncService() *CollectSyncService {
	return &CollectSyncService{
		videoService: NewUserInboxVideoRelatedService(),
	}
}

// SyncUserCollects 同步用户收藏夹
// userUUID: 用户UUID
// collectSourceId: 收藏夹来源ID（可以是收藏夹ID或其他标识）
// awemeIds: 从抖音API获取的收藏视频ID列表
func (s *CollectSyncService) SyncUserCollects(userUUID, collectSourceId string, awemeIds []string) (*SyncResult, error) {
	result := &SyncResult{
		UserUUID:      userUUID,
		SourceId:      collectSourceId,
		TotalAwemeIds: len(awemeIds),
		StartTime:     time.Now(),
	}

	// 参数验证
	if userUUID == "" {
		return result, fmt.Errorf("用户UUID不能为空")
	}
	if collectSourceId == "" {
		return result, fmt.Errorf("收藏夹来源ID不能为空")
	}
	if len(awemeIds) == 0 {
		result.EndTime = time.Now()
		return result, nil // 没有需要同步的视频
	}

	// 批量创建视频记录
	err := s.videoService.BatchCreateFromCollectSync(userUUID, collectSourceId, awemeIds)
	if err != nil {
		result.EndTime = time.Now()
		result.Error = err.Error()
		return result, fmt.Errorf("批量创建视频记录失败: %w", err)
	}

	// 获取实际创建的记录数（通过检查数据库中的记录）
	createdCount, err := s.getCreatedRecordsCount(userUUID, collectSourceId, awemeIds)
	if err != nil {
		result.EndTime = time.Now()
		result.Error = err.Error()
		return result, fmt.Errorf("获取创建记录数失败: %w", err)
	}

	result.CreatedCount = createdCount
	result.SkippedCount = result.TotalAwemeIds - createdCount
	result.EndTime = time.Now()
	result.Success = true

	return result, nil
}

// getCreatedRecordsCount 获取已创建的记录数
func (s *CollectSyncService) getCreatedRecordsCount(userUUID, sourceId string, awemeIds []string) (int, error) {
	existingIds, err := s.videoService.getExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		return 0, err
	}
	return len(existingIds), nil
}

// ProcessPendingCollects 处理待处理的收藏视频
// 将状态为 pending 的记录标记为已处理或失败
func (s *CollectSyncService) ProcessPendingCollects(userUUID string, batchSize int) (*ProcessResult, error) {
	result := &ProcessResult{
		UserUUID:  userUUID,
		BatchSize: batchSize,
		StartTime: time.Now(),
	}

	// 获取待处理的记录
	pendingRecords, err := s.videoService.GetPendingRecords(userUUID, batchSize)
	if err != nil {
		result.EndTime = time.Now()
		result.Error = err.Error()
		return result, fmt.Errorf("获取待处理记录失败: %w", err)
	}

	result.TotalRecords = len(pendingRecords)
	if len(pendingRecords) == 0 {
		result.EndTime = time.Now()
		return result, nil // 没有待处理的记录
	}

	// 处理每条记录
	var successIds, failedIds []string
	for _, record := range pendingRecords {
		// 这里可以添加具体的处理逻辑，比如：
		// 1. 调用抖音API获取视频详情
		// 2. 处理视频内容
		// 3. 更新处理状态

		// 示例：简单的处理逻辑（实际应用中需要根据业务需求实现）
		if s.processVideoRecord(record) {
			successIds = append(successIds, record.AwemeId)
		} else {
			failedIds = append(failedIds, record.AwemeId)
		}
	}

	// 批量更新成功的记录
	if len(successIds) > 0 {
		err = s.videoService.BatchUpdateHandleStatus(successIds, userUUID, "success")
		if err != nil {
			result.Error = fmt.Sprintf("更新成功状态失败: %v", err)
		} else {
			result.SuccessCount = len(successIds)
		}
	}

	// 批量更新失败的记录
	if len(failedIds) > 0 {
		err = s.videoService.BatchUpdateHandleStatus(failedIds, userUUID, "failed")
		if err != nil {
			result.Error = fmt.Sprintf("更新失败状态失败: %v", err)
		} else {
			result.FailedCount = len(failedIds)
		}
	}

	result.EndTime = time.Now()
	result.Success = result.Error == ""

	return result, nil
}

// processVideoRecord 处理单个视频记录的业务逻辑
// 这里只是示例，实际应用中需要根据具体业务需求实现
func (s *CollectSyncService) processVideoRecord(record *entity.UserInboxVideoRelated) bool {
	// 示例处理逻辑：
	// 1. 检查视频是否还存在
	// 2. 获取视频详细信息
	// 3. 执行相关业务处理

	// 这里简单返回 true 表示处理成功
	// 实际应用中应该实现具体的处理逻辑
	return true
}

// GetSyncStats 获取同步统计信息
func (s *CollectSyncService) GetSyncStats(userUUID string) (*CollectSyncStats, error) {
	stats, err := s.videoService.GetStatsByUser(userUUID)
	if err != nil {
		return nil, fmt.Errorf("获取用户统计信息失败: %w", err)
	}

	syncStats := &CollectSyncStats{
		UserUUID:    userUUID,
		TotalVideos: stats["total"].(int64),
		StatusStats: make(map[string]int),
		TypeStats:   make(map[string]int),
	}

	// 处理状态统计
	if statusStats, ok := stats["status_stats"].([]map[string]interface{}); ok {
		for _, stat := range statusStats {
			status := stat["handle_status"].(string)
			count := int(stat["count"].(int64))
			syncStats.StatusStats[status] = count
		}
	}

	// 处理类型统计
	if typeStats, ok := stats["type_stats"].([]map[string]interface{}); ok {
		for _, stat := range typeStats {
			sourceType := stat["source_type"].(string)
			count := int(stat["count"].(int64))
			syncStats.TypeStats[sourceType] = count
		}
	}

	return syncStats, nil
}

// SyncResult 同步结果
type SyncResult struct {
	UserUUID      string    `json:"user_uuid"`
	SourceId      string    `json:"source_id"`
	TotalAwemeIds int       `json:"total_aweme_ids"`
	CreatedCount  int       `json:"created_count"`
	SkippedCount  int       `json:"skipped_count"`
	Success       bool      `json:"success"`
	Error         string    `json:"error,omitempty"`
	StartTime     time.Time `json:"start_time"`
	EndTime       time.Time `json:"end_time"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	UserUUID     string    `json:"user_uuid"`
	BatchSize    int       `json:"batch_size"`
	TotalRecords int       `json:"total_records"`
	SuccessCount int       `json:"success_count"`
	FailedCount  int       `json:"failed_count"`
	Success      bool      `json:"success"`
	Error        string    `json:"error,omitempty"`
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
}

// CollectSyncStats 收藏夹同步统计
type CollectSyncStats struct {
	UserUUID    string         `json:"user_uuid"`
	TotalVideos int64          `json:"total_videos"`
	StatusStats map[string]int `json:"status_stats"`
	TypeStats   map[string]int `json:"type_stats"`
}
