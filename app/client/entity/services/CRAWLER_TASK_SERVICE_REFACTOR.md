# CrawlerTaskService 重构文档

## 📋 重构概述

**日期**: 2024-06-23  
**文件**: `app/client/entity/services/crawler_task_service.go`  
**重构目标**: 将用户任务查询基于 `user_crawler_keywords` 实体，实现更合理的数据架构

## 🎯 重构原因

### 原有问题
1. **直接查询问题**: 原来的 `GetUserTasks` 方法直接通过 `user_uuid` 查询 `crawler_tasks` 表
2. **架构不合理**: 忽略了 `user_crawler_keywords` 这个重要的关联实体
3. **权限控制缺失**: 没有通过用户关键词关联来控制访问权限
4. **数据一致性**: 用户任务应该基于用户的关键词关联，而不是直接关联

### 新架构优势
1. **数据完整性**: 通过 `user_crawler_keywords` 确保数据关联的完整性
2. **权限控制**: 用户只能看到自己关联的关键词相关的任务
3. **扩展性**: 支持用户个性化配置（别名、优先级、分类等）
4. **统计准确**: 基于用户实际关联的关键词进行统计

## 🔄 重构详情

### 1. GetUserTasks 方法重构

**重构前**:
```go
func (s *CrawlerTaskService) GetUserTasks(userUUID string, page, pageSize int, status, platform string) ([]*entity.CrawlerTask, int64, error) {
    query := setting.CrawlerModel("crawler_tasks").
        Where("user_uuid", userUUID).  // 直接通过用户UUID查询
        Where("is_deleted", 0)
    // ... 其他逻辑
}
```

**重构后**:
```go
func (s *CrawlerTaskService) GetUserTasks(userUUID string, page, pageSize int, status, platform string) ([]*entity.CrawlerTask, int64, error) {
    // 第一步：获取用户的关键词列表
    userKeywords, _, err := s.userKeywordService.GetUserKeywords(userUUID, 1, 1000, "", true)
    
    // 第二步：根据关键词ID查询爬虫任务
    query := setting.CrawlerModel("crawler_tasks").
        Where("keyword_id IN (?)", keywordIDs).  // 通过关键词ID查询
        Where("is_deleted", 0)
    // ... 其他逻辑
}
```

### 2. 新增 GetUserTasksWithKeywordInfo 方法

```go
func (s *CrawlerTaskService) GetUserTasksWithKeywordInfo(userUUID string, page, pageSize int, status, platform string) ([]*entity.CrawlerTask, []*entity.UserCrawlerKeyword, int64, error)
```

**功能**: 同时返回任务列表和用户关键词信息，便于前端展示关键词详情

### 3. GetUserTasksByKeywords 方法增强

**新增功能**:
- **权限验证**: 验证用户对指定关键词的访问权限
- **安全过滤**: 只返回用户实际拥有的关键词相关任务
- **空值处理**: 当没有指定关键词时，自动获取用户所有关键词

### 4. GetTaskStats 方法重构

**重构前**: 直接统计用户的所有任务
**重构后**: 只统计用户关键词相关的任务，并新增关键词维度统计

## 📊 数据流程图

```
用户请求任务列表
        ↓
1. 查询 user_crawler_keywords 表
   获取用户关联的关键词ID列表
        ↓
2. 使用关键词ID列表查询 crawler_tasks 表
   获取相关任务
        ↓
3. 返回任务列表和统计信息
```

## 🔧 API 变更

### 方法签名变更

#### GetUserTasks
- **签名**: 保持不变
- **行为**: 内部逻辑重构，基于用户关键词查询

#### GetUserTasksWithKeywordInfo (新增)
- **签名**: `([]*entity.CrawlerTask, []*entity.UserCrawlerKeyword, int64, error)`
- **用途**: 同时返回任务和关键词信息

#### GetUserTasksByKeywords
- **签名**: 保持不变
- **行为**: 增加权限验证和安全过滤

#### GetTaskStats
- **签名**: 保持不变
- **行为**: 基于用户关键词统计，新增关键词维度

### 返回数据变更

#### GetTaskStats 新增字段
```go
stats["keyword_stats"] = []gf.Map{
    {"keyword_id": 1, "count": 10},
    {"keyword_id": 2, "count": 5},
    // ...
}
```

## 🛡️ 安全性改进

### 1. 权限控制
- 用户只能访问自己关联的关键词相关任务
- 防止通过关键词ID越权访问其他用户的任务

### 2. 数据隔离
- 通过 `user_crawler_keywords` 实现用户数据隔离
- 确保数据访问的合法性

### 3. 输入验证
- 验证关键词ID的有效性
- 过滤无效或无权限的关键词ID

## 📈 性能考虑

### 1. 查询优化
- 使用 IN 查询批量获取任务
- 避免 N+1 查询问题

### 2. 索引建议
```sql
-- user_crawler_keywords 表索引
CREATE INDEX idx_user_keyword_active ON user_crawler_keywords (user_uuid, is_active);
CREATE INDEX idx_keyword_id ON user_crawler_keywords (keyword_id);

-- crawler_tasks 表索引
CREATE INDEX idx_keyword_id_status ON crawler_tasks (keyword_id, status);
CREATE INDEX idx_keyword_id_platform ON crawler_tasks (keyword_id, platform);
```

### 3. 缓存策略
- 可以考虑缓存用户关键词列表
- 减少频繁的关键词查询

## 🔄 迁移指南

### 1. 代码更新
现有调用 `GetUserTasks` 的代码无需修改，方法签名保持兼容。

### 2. 新功能使用
```go
// 获取任务和关键词信息
tasks, keywords, total, err := service.GetUserTasksWithKeywordInfo(userUUID, 1, 20, "", "")

// 安全的关键词过滤查询
tasks, total, err := service.GetUserTasksByKeywords(userUUID, []int{1, 2, 3}, 1, 20, "", "")
```

### 3. 统计数据更新
前端需要适配新的 `keyword_stats` 统计维度。

## ✅ 测试建议

### 1. 单元测试
- 测试用户无关键词的情况
- 测试权限验证逻辑
- 测试关键词ID过滤

### 2. 集成测试
- 测试跨数据库查询的正确性
- 测试分页和排序功能
- 测试统计数据的准确性

### 3. 性能测试
- 测试大量关键词的查询性能
- 测试分页查询的响应时间

## 📝 后续优化

### 1. 缓存优化
- 实现用户关键词列表缓存
- 实现任务统计缓存

### 2. 查询优化
- 考虑使用联表查询优化性能
- 实现更复杂的过滤条件

### 3. 监控告警
- 添加查询性能监控
- 添加数据一致性检查

---

**重构完成**: ✅ CrawlerTaskService 已成功重构为基于 user_crawler_keywords 的架构
