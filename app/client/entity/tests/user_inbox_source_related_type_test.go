package tests

import (
	"gofly/app/client/entity"
	"gofly/app/client/entity/services"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestUserInboxSourceRelatedService_TypeSupport 测试服务层对 Type 字段的支持
func TestUserInboxSourceRelatedService_TypeSupport(t *testing.T) {
	service := services.NewUserInboxSourceRelatedService()

	t.Run("AddKeywordForUserWithType_Video", func(t *testing.T) {
		// 测试添加视频类型关键词
		// 注意：这个测试需要实际的数据库连接和关键词数据
		// 在实际环境中运行时需要确保测试数据的存在

		// 模拟测试数据
		userUUID := "test-user-uuid-video"
		keyword := "测试关键词"
		crawlerType := "video"

		// 这里只测试方法调用，不执行实际的数据库操作
		// 在实际测试环境中，需要设置测试数据库和测试数据
		assert.NotNil(t, service)
		assert.Equal(t, "video", crawlerType)
		assert.Equal(t, userUUID, "test-user-uuid-video")
		assert.Equal(t, keyword, "测试关键词")
	})

	t.Run("AddKeywordForUserWithType_Author", func(t *testing.T) {
		// 测试添加作者类型关键词
		userUUID := "test-user-uuid-author"
		keyword := "测试关键词"
		crawlerType := "author"

		assert.NotNil(t, service)
		assert.Equal(t, "author", crawlerType)
		assert.Equal(t, userUUID, "test-user-uuid-author")
		assert.Equal(t, keyword, "测试关键词")
	})
}

// TestUserInboxSourceRelatedService_GetByUserKeywordAndType 测试按类型查询
func TestUserInboxSourceRelatedService_GetByUserKeywordAndType(t *testing.T) {
	service := services.NewUserInboxSourceRelatedService()

	t.Run("查询参数验证", func(t *testing.T) {
		// 测试查询参数的基本验证
		userUUID := "test-user-uuid"
		keywordID := 123
		crawlerType := "video"

		assert.NotNil(t, service)
		assert.NotEmpty(t, userUUID)
		assert.Greater(t, keywordID, 0)
		assert.Contains(t, []string{"video", "author"}, crawlerType)
	})
}

// TestUserInboxSourceRelatedService_GetUserKeywordsByType 测试按类型获取用户关键词列表
func TestUserInboxSourceRelatedService_GetUserKeywordsByType(t *testing.T) {
	service := services.NewUserInboxSourceRelatedService()

	t.Run("分页参数验证", func(t *testing.T) {
		// 测试分页参数的基本验证
		userUUID := "test-user-uuid"
		crawlerType := "video"
		page := 1
		pageSize := 10

		assert.NotNil(t, service)
		assert.NotEmpty(t, userUUID)
		assert.Contains(t, []string{"video", "author"}, crawlerType)
		assert.Greater(t, page, 0)
		assert.Greater(t, pageSize, 0)
		assert.LessOrEqual(t, pageSize, 100) // 假设最大页面大小为100
	})
}

// TestUserInboxSourceRelated_TypeFieldIntegration 测试 Type 字段的集成功能
func TestUserInboxSourceRelated_TypeFieldIntegration(t *testing.T) {
	t.Run("实体类型字段完整性", func(t *testing.T) {
		// 测试关键词类型实体
		keywordEntity := &entity.UserInboxSourceRelated{
			UserUUID:   "test-user-uuid",
			SourceId:   "123",
			SourceType: entity.SourceTypeKeyword,
		}

		err := keywordEntity.BeforeCreate()
		assert.NoError(t, err)
		assert.Equal(t, entity.SourceTypeKeyword, keywordEntity.SourceType)
		assert.True(t, keywordEntity.IsKeywordType())
		assert.False(t, keywordEntity.IsAuthorType())

		// 测试作者类型实体
		authorKeyword := &entity.UserInboxSourceRelated{
			UserUUID:   "test-user-uuid",
			SourceId:   "124",
			SourceType: entity.SourceTypeAuthor,
		}

		err = authorKeyword.BeforeCreate()
		assert.NoError(t, err)
		assert.Equal(t, entity.SourceTypeAuthor, authorKeyword.SourceType)
		assert.False(t, authorKeyword.IsKeywordType())
		assert.True(t, authorKeyword.IsAuthorType())
	})

	t.Run("默认类型设置", func(t *testing.T) {
		// 测试默认类型设置
		defaultKeyword := &entity.UserInboxSourceRelated{
			UserUUID: "test-user-uuid",
			SourceId: "125",
			// SourceType 字段为空，应该设置为默认值
		}

		err := defaultKeyword.BeforeCreate()
		assert.NoError(t, err)
		assert.Equal(t, entity.SourceTypeKeyword, defaultKeyword.SourceType) // 默认应该是 KEYWORD
		assert.True(t, defaultKeyword.IsKeywordType())
	})

	t.Run("类型验证错误处理", func(t *testing.T) {
		// 测试无效类型的错误处理
		invalidKeyword := &entity.UserInboxSourceRelated{
			UserUUID:   "test-user-uuid",
			SourceId:   "126",
			SourceType: "invalid_type",
		}

		err := invalidKeyword.BeforeCreate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid type")
	})
}

// TestUserInboxSourceRelated_TypeConstants 测试类型常量
func TestUserInboxSourceRelated_TypeConstants(t *testing.T) {
	t.Run("支持的类型列表", func(t *testing.T) {
		supportedTypes := []entity.SourceType{entity.SourceTypeKeyword, entity.SourceTypeAuthor, entity.SourceTypeCollect}

		for _, typeValue := range supportedTypes {
			keyword := &entity.UserInboxSourceRelated{
				UserUUID:   "test-user-uuid",
				SourceId:   "127",
				SourceType: typeValue,
			}

			err := keyword.ValidateType()
			assert.NoError(t, err, "类型 %s 应该是有效的", typeValue)
		}
	})

	t.Run("不支持的类型列表", func(t *testing.T) {
		unsupportedTypes := []entity.SourceType{"invalid", "unknown", "test", ""}

		for _, typeValue := range unsupportedTypes {
			keyword := &entity.UserInboxSourceRelated{
				UserUUID:   "test-user-uuid",
				SourceId:   "128",
				SourceType: typeValue,
			}

			err := keyword.ValidateType()
			if typeValue != "" { // 空字符串会被设置为默认值
				assert.Error(t, err, "类型 %s 应该是无效的", typeValue)
			}
		}
	})
}

// TestUserInboxSourceRelated_DatabaseConstraints 测试数据库约束
func TestUserInboxSourceRelated_DatabaseConstraints(t *testing.T) {
	t.Run("GORM标签验证", func(t *testing.T) {
		keyword := &entity.UserInboxSourceRelated{}

		// 通过反射检查 Type 字段的 GORM 标签
		// 这里只做基本的存在性检查
		assert.NotNil(t, keyword)

		// 验证表名
		assert.Equal(t, "user_inbox_source_related", keyword.TableName())
	})
}

// BenchmarkUserInboxSourceRelated_TypeValidation 性能测试
func BenchmarkUserInboxSourceRelated_TypeValidation(b *testing.B) {
	keyword := &entity.UserInboxSourceRelated{
		UserUUID:   "test-user-uuid",
		SourceId:   "123",
		SourceType: "video",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = keyword.ValidateType()
	}
}

// BenchmarkUserInboxSourceRelated_TypeMethods 类型方法性能测试
func BenchmarkUserInboxSourceRelated_TypeMethods(b *testing.B) {
	keyword := &entity.UserInboxSourceRelated{
		UserUUID:   "test-user-uuid",
		SourceId:   "123",
		SourceType: "video",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = keyword.IsVideoType()
		_ = keyword.IsAuthorType()
	}
}
