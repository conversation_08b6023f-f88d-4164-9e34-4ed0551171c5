package assets

import (
	"gofly/app/client/entity"
	_ "gofly/utils/drivers/mysql"
	"gofly/utils/gf"
	"gofly/utils/tools/glog"
	"gofly/utils/tools/gmap"
	"testing"
)

func TestGetList(t *testing.T) {
	var userUUID = "18e0bd37d19142fe9623707a1e6b28fe"
	whereMap := gmap.New()
	whereMap.Set("is_deleted", 0)
	whereMap.Set("user_uuid", userUUID)
	// MDB := gf.Model("assets").Where(whereMap)
	var assets []*entity.Assets

	MDB := gf.Model(assets).With(entity.Topic{})

	var keyword = "万万没想到"
	if keyword != "" {
		MDB.Where(MDB.Builder().WhereOr("video_title LIKE ?", "%"+keyword+"%").WhereOr("script_type LIKE ?", "%"+keyword+"%"))
	}
	// MDB.Where("uuid", "d4aac7907ab24db082a9c1fe7f77747b")
	MDB.Order("create_time desc").Scan(&assets)

	glog.Info(t.Context(), "assets", assets)
}
