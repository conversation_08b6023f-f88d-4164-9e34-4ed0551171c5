package assets_utils

import (
	"gofly/app/client/cons"
	"gofly/app/client/entity"
	_ "gofly/utils/drivers/mysql"
	_ "gofly/utils/drivers/redis"
	"gofly/utils/gf"
	"testing"
)

func TestAdd(t *testing.T) {
	var assetUUID = "62268c2a41964f6c9387e34e4e8e24aa"
	var userUUID = "18e0bd37d19142fe9623707a1e6b28fe"
	errorCount, err := gf.Model("tech_coze_workflow_run_history").Where("assets_uuid", assetUUID).Where("user_uuid", userUUID).Count()
	if err != nil {
		errorCount = 0
	}
	gf.Model("assets").Where("uuid", assetUUID).Data(map[string]interface{}{
		"generation_status": map[bool]string{true: cons.Failed.Value, false: cons.Error.Value}[errorCount <= 3],
	}).Update()
}

func TestJointVisionAnalysis(t *testing.T) {
	// 测试用例
	asset := entity.Assets{
		AnalysisResults: "这是视频分析结果的内容，包含了对视频的详细分析。",
		AsrText:         "这是视频的音频转文字内容，这台机器的出现重新定义了什么叫做洗车机的性价比。",
	}

	result := JointVisionAnalysis(asset)

	expected := "这是视频分析结果的内容，包含了对视频的详细分析。\n\n# 视频原文\n```这是视频的音频转文字内容，这台机器的出现重新定义了什么叫做洗车机的性价比。```"

	if result != expected {
		t.Errorf("JointVisionAnalysis() 结果不匹配\n期望: %s\n实际: %s", expected, result)
	}

	t.Logf("拼接结果:\n%s", result)
}

func TestJointVisionAnalysisEmpty(t *testing.T) {
	// 测试空值情况
	asset := entity.Assets{
		AnalysisResults: "",
		AsrText:         "",
	}

	result := JointVisionAnalysis(asset)
	expected := "\n\n# 视频原文\n``````"

	if result != expected {
		t.Errorf("JointVisionAnalysis() 空值测试失败\n期望: %s\n实际: %s", expected, result)
	}
}

func TestJointVisionAnalysisWithNewlines(t *testing.T) {
	// 测试包含换行符的情况
	asset := entity.Assets{
		AnalysisResults: "第一行分析结果\n第二行分析结果\n第三行分析结果",
		AsrText:         "第一句话。\n第二句话。\n第三句话。",
	}

	result := JointVisionAnalysis(asset)

	expected := "第一行分析结果\n第二行分析结果\n第三行分析结果\n\n# 视频原文\n```第一句话。\n第二句话。\n第三句话。```"

	if result != expected {
		t.Errorf("JointVisionAnalysis() 换行符测试失败\n期望: %s\n实际: %s", expected, result)
	}

	t.Logf("包含换行符的拼接结果:\n%s", result)
}

// TestPreParseAssetVideoMetaInfoWithURL 测试已移动到 service/assets/workflow/handler 包
// 因为 PreParseAssetVideoMetaInfo 函数已经移动到 service 层
// 为了避免导入循环，这个测试应该在 service 层进行
