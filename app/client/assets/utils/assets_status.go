package assets_utils

import (
	"gofly/app/client/cons"
	"gofly/app/client/entity"
	"gofly/app/client/utils"
	"gofly/utils/gf"
	"gofly/utils/tools/gctx"
	"gofly/utils/tools/gtime"
	"time"
)

func HandleDoingGenrationAssets() {
	var doingAssets []*entity.Assets
	err := gf.Model("assets").
		Where("analysis_status", cons.Success.Value).
		Where("generation_status", cons.Doing.Value).
		WhereLT("update_time", gtime.Now().Add(-time.Duration(5)*time.Minute)).
		Scan(&doingAssets)

	if err != nil {
		gf.Log().Print(gctx.New(), "handleDoingGenrationAssets 查询错误", err)
		return
	}

	for _, asset := range doingAssets {
		gf.Log().Print(gctx.New(), "handleDoingGenrationAssets 开始处理", asset.UUID)
		HandleAssetTopicsWillFinishV2(*asset)
	}
}

func HandlePendingGenrationAssets() {
	var pendingAssets []*entity.Assets
	err := gf.Model("assets").
		WhereIn("analysis_status", []string{cons.Doing.Value, cons.Success.Value}).
		WhereIn("generation_status", []string{cons.Failed.Value, cons.Pending.Value}).
		WhereLT("update_time", gtime.Now().Add(-time.Duration(5)*time.Minute)).
		Scan(&pendingAssets)

	if err != nil {
		gf.Log().Print(gctx.New(), "handlePendingGenrationAssets 查询错误", err)
		return
	}

	for _, asset := range pendingAssets {
		gf.Log().Print(gctx.New(), "handlePendingGenrationAssets 开始处理", asset.UUID)
		HandleAssetAnalysisWillFinishV2(*asset)
	}
}

func HandlePendingAnalysisAssets() {
	var doingAssets []*entity.Assets
	err := gf.Model("assets").
		WhereIn("analysis_status", []string{cons.Pending.Value, cons.Failed.Value}).
		WhereLT("update_time", gtime.Now().Add(-time.Duration(15)*time.Minute)).
		Scan(&doingAssets)

	if err != nil {
		gf.Log().Print(gctx.New(), "HandlePendingAnalysisAssets 查询错误", err)
		return
	}

	for _, asset := range doingAssets {
		gf.Log().Print(gctx.New(), "HandlePendingAnalysisAssets 开始处理", asset.UUID)
		RunAnalysisWorkflowV2(*asset)
	}
}

func CompensationWorkflow() {
	HandleDoingGenrationAssets()
	HandlePendingGenrationAssets()

	HandlePendingAnalysisAssets()
}

func groupingBy[K comparable, T any](vs []T, f func(T) K) map[K][]T {
	group := make(map[K][]T)
	for _, v := range vs {
		k := f(v)
		group[k] = append(group[k], v)
	}
	return group
}

func HandleTotalAssetsStatusCache() {
	var (
		ctx           = gctx.New()
		pendingAssets []*entity.Assets
	)
	MDB := gf.Model("assets").WhereLT("update_time", gtime.Now().Add(-time.Duration(5)*time.Minute))
	err := MDB.Where(MDB.Builder().
		WhereOrIn("analysis_status", []string{cons.Doing.Value, cons.Pending.Value}).
		WhereOrIn("generation_status", []string{cons.Doing.Value, cons.Pending.Value})).
		Scan(&pendingAssets)
	if err != nil {
		gf.Log().Print(ctx, "HandleTotalAssetsStatusCache 错误", err)
		return
	}

	userUUIDMap := groupingBy(pendingAssets, func(t *entity.Assets) string {
		return t.UserUUID
	})

	for userUUID, assetsList := range userUUIDMap {
		assetStatusMap := map[string]interface{}{}
		for _, asset := range assetsList {
			if asset.GenerationStatus == cons.Doing.Value {
				assetStatusMap[asset.UUID] = cons.GENERATION.Value
			} else {
				assetStatusMap[asset.UUID] = cons.ANALYSIS.Value
			}
		}
		gf.Redis().HSet(ctx, utils.TotalUserAssetsStatusHashKey(userUUID), assetStatusMap)
		gf.Redis().Expire(ctx, utils.TotalUserAssetsStatusHashKey(userUUID), int64(time.Hour.Seconds()))
	}

}
