package assets_utils

import (
	"context"
	"errors"
	"gofly/setting/ko"
	"gofly/utils/gf"
	"gofly/utils/tools/gctx"
	"time"

	"github.com/coze-dev/coze-go"
)

func RunWorkflow(
	params coze.RunWorkflowsReq,
) (*coze.RunWorkflowsResp, error) {
	var (
		gfctx       = gctx.New()
		sessionName = ""
		cozeCli     = ko.CozeClient(sessionName)
	)

	ctx := context.Background()
	resp, err := cozeCli.Workflows.Runs.Create(ctx, &params)
	if err != nil {
		gf.Log().Print(gfctx, "Error running workflow:", err)
		return nil, err
	}
	return resp, nil
}

func AsyncRunWorkflow(
	params coze.RunWorkflowsReq,
	callback func(resp *coze.RunWorkflowsResp),
) {
	var (
		gfctx       = gctx.New()
		sessionName = ""
		cozeCli     = ko.CozeClient(sessionName)
	)

	ctx := context.Background()
	resp, err := cozeCli.Workflows.Runs.Create(ctx, &params)
	if err != nil {
		gf.Log().Print(gfctx, "Error running workflow:", err)
		return
	}
	callback(resp)
}

type WorkflowRunHistoryParameters struct {
	Token       string
	WorkflowReq coze.RetrieveWorkflowsRunsHistoriesReq
}

func GetWorkflowRunHistoryUntilSuccess(
	params coze.RetrieveWorkflowsRunsHistoriesReq,
	successCallback func(history *coze.RetrieveWorkflowRunsHistoriesResp),
	failCallback func(history *coze.RetrieveWorkflowRunsHistoriesResp),
) {
	var (
		sessionName = ""
		cozeCli     = ko.CozeClient(sessionName)
	)

	ctx := context.Background()
	isFinished := false
	for !isFinished {
		historyResp, err := cozeCli.Workflows.Runs.Histories.Retrieve(ctx, &params)
		if err != nil {
			gf.Log().Print(ctx, "Error retrieving history:", err)
			return
		}

		history := historyResp.Histories[0]
		switch history.ExecuteStatus {
		case coze.WorkflowExecuteStatusFail:
			failCallback(historyResp)
			gf.Log().Print(ctx, "Workflow runs failed, reason:", history.ErrorMessage)
			isFinished = true
		case coze.WorkflowExecuteStatusRunning:
			gf.Log().Print(ctx, "Workflow runs is running")
			time.Sleep(time.Second * 5)
		default:
			successCallback(historyResp)
			gf.Log().Print(ctx, "Workflow finished")
			isFinished = true
		}
	}
}

func CheckWorkflowRunHistoryUntilCompletion(
	params *coze.RetrieveWorkflowsRunsHistoriesReq,
) (*coze.RetrieveWorkflowRunsHistoriesResp, error) {
	var (
		sessionName = ""
		cozeCli     = ko.CozeClient(sessionName)
	)

	ctx := context.Background()
	isFinished := false
	for !isFinished {
		historyResp, err := cozeCli.Workflows.Runs.Histories.Retrieve(ctx, params)
		if err != nil {
			gf.Log().Print(ctx, "Error retrieving history:", err)
			return nil, err
		}

		history := historyResp.Histories[0]
		switch history.ExecuteStatus {
		case coze.WorkflowExecuteStatusFail:
			// failCallback(historyResp)
			gf.Log().Print(ctx, "Workflow runs failed, reason:", history.ErrorMessage)
			isFinished = true
			return nil, errors.New("Workflow runs failed, reason: " + history.ErrorMessage)
		case coze.WorkflowExecuteStatusRunning:
			gf.Log().Print(ctx, "Workflow runs is running")
			time.Sleep(time.Second * 5)
		default:
			// successCallback(historyResp)
			gf.Log().Print(ctx, "Workflow finished")
			isFinished = true
			return historyResp, nil // Return the history response when it's successful, not the resul
		}
	}
	return nil, errors.New("未预期的退出") // Return the history response when it's successful, not the resul
}
