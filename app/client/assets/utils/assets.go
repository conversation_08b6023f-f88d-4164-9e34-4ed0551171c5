package assets_utils

import (
	"context"
	"gofly/app/client/cons"
	coze_dto "gofly/app/client/dto/coze"
	"gofly/app/client/entity"
	"gofly/utils/gf"
	"gofly/utils/tools/garray"
	"gofly/utils/tools/gconv"
	"gofly/utils/tools/gctx"
	"gofly/utils/tools/gjson"
	"gofly/utils/tools/glog"
	"net/url"

	"github.com/coze-dev/coze-go"
)

type WorkflowVideoInfo struct {
	AnalysisStatus  string `json:"analysis_status"`
	AnalysisResults string `json:"analysis_results"`
	ScriptType      string `json:"script_type"`
	AsrText         string `json:"asr_text"`
}

// 替换第30-31行的 JSON 字符串

type AnalysisCallbackResponse struct {
	CallbackURL string               `json:"callback_url"`
	Data        AnalysisResponseData `json:"data"`
	Msg         string               `json:"msg"`
	Success     bool                 `json:"success"`
}

type AnalysisResponseData struct {
	AsrText        string `json:"asr_text"`
	ScriptType     string `json:"script_type"`
	TopicTitle     string `json:"topic_title"`
	VideoContent   string `json:"video_content"`
	VideoSummary   string `json:"video_summary"`
	VideoTags      string `json:"video_tags"`
	ViralElements  string `json:"viral_elements"`
	AnalysisStatus string `json:"analysis_status"`
}

type TopicListResponse struct {
	TopicList []entity.Topic `json:"topicList"`
}
type GenerationCallbackResponse struct {
	CallbackURL string            `json:"callback_url"`
	Data        TopicListResponse `json:"data"`
	Msg         string            `json:"msg"`
	Success     bool              `json:"success"`
}

func RunAnalysisWorkflow(asset entity.Assets) {
	var gfctx = gctx.New()
	var workflowId, _ = gf.Cfg().Get(gfctx, "coze.default.analysis.workflowId")
	var data = coze_dto.AnalysisWorkflowParams{
		DouyinUrl:  asset.DouyinURL,
		Insight:    asset.Insight,
		AssetsUUID: asset.UUID,
	}
	AsyncRunWorkflow(coze.RunWorkflowsReq{
		WorkflowID: workflowId.String(),
		Parameters: gconv.Map(data),
		IsAsync:    true,
	}, func(resp *coze.RunWorkflowsResp) {
		gf.Log().Print(gfctx, "callback resp:", resp)
		var workflowParams = coze.RetrieveWorkflowsRunsHistoriesReq{
			WorkflowID: workflowId.String(),
			ExecuteID:  resp.ExecuteID,
		}
		var analysis_coze_id, _ = gjson.Marshal(entity.LocalRetrieveWorkflowsRunsHistoriesRes{
			RetrieveWorkflowsRunsHistoriesReq: workflowParams,
			DebugURL:                          resp.DebugURL,
		})
		gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
			"analysis_status":  cons.Doing.Value,
			"analysis_coze_id": analysis_coze_id,
		}).Update()

		// utils.PublishMessage(
		// 	asset.UserUUID,
		// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.ANALYSIS.Value, ChangeStatus: cons.Doing.Value},
		// )
	})
}

func RunAnalysisWorkflowV2(asset entity.Assets) {
	var gfctx = gctx.New()
	var workflowId, _ = gf.Cfg().Get(gfctx, "assets.default.analysis.workflowId")
	var callbackUrl, _ = gf.Cfg().Get(gfctx, "assets.default.analysis.callbackUrl")
	uri, err := url.Parse(callbackUrl.String())
	if err != nil {
		return
	}
	v := url.Values{}
	v.Add("uuid", asset.UUID)
	v.Add("type", cons.ANALYSIS.Value)
	uri.RawQuery = v.Encode()

	var data = coze_dto.AnalysisWorkflowParamsV2{
		CallbackUrl:    uri.String(),
		VideoTitle:     asset.VideoTitle,
		VisionAnalysis: asset.AnalysisResults,
	}
	AsyncRunWorkflow(coze.RunWorkflowsReq{
		WorkflowID: workflowId.String(),
		Parameters: gconv.Map(data),
		IsAsync:    true,
	}, func(resp *coze.RunWorkflowsResp) {
		gf.Log().Print(gfctx, "callback resp:", resp)
		var workflowParams = coze.RetrieveWorkflowsRunsHistoriesReq{
			WorkflowID: workflowId.String(),
			ExecuteID:  resp.ExecuteID,
		}
		var analysis_coze_id, _ = gjson.Marshal(entity.LocalRetrieveWorkflowsRunsHistoriesRes{
			RetrieveWorkflowsRunsHistoriesReq: workflowParams,
			DebugURL:                          resp.DebugURL,
		})
		gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
			"analysis_status":  cons.Doing.Value,
			"analysis_coze_id": analysis_coze_id,
		}).Update()

		// utils.PublishMessage(
		// 	asset.UserUUID,
		// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.ANALYSIS.Value, ChangeStatus: cons.Doing.Value},
		// )
	})
}

func HandleAssetAnalysisWillFinish(asset entity.Assets) {
	var req *coze.RetrieveWorkflowsRunsHistoriesReq
	if err := gconv.Struct(asset.AnalysisCozeID, &req); err != nil {
		return
	}

	GetWorkflowRunHistoryUntilSuccess(*req,
		func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
			var response struct {
				Output string `json:"Output"`
			}
			gjson.Unmarshal([]byte(history.Histories[0].Output), &response)
			var videoInfo WorkflowVideoInfo
			gjson.Unmarshal([]byte(response.Output), &videoInfo)
			videoInfo.AnalysisStatus = cons.Success.Value
			gf.Model("assets").Where("uuid", asset.UUID).Data(videoInfo).Update()
			RunTopicsWorkflow(asset)
			// utils.PublishMessage(
			// 	asset.UserUUID,
			// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.ANALYSIS.Value, ChangeStatus: cons.Success.Value},
			// )
		},
		func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
			workflowRunHistory := new(coze_dto.CozeWorkflowExecuteStatusLog)
			gconv.Struct(history.Histories[0], workflowRunHistory)
			workflowRunHistory.UserUUID = asset.UserUUID
			workflowRunHistory.AssetsUUID = asset.UUID
			gf.Model("tech_coze_workflow_run_history").Save(workflowRunHistory)
			errorCount, err := gf.Model("tech_coze_workflow_run_history").Where("assets_uuid", asset.UUID).Where("user_uuid", asset.UserUUID).Count()
			if err != nil {
				errorCount = 0
			}
			gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
				"analysis_status": map[bool]string{true: cons.Failed.Value, false: cons.Error.Value}[errorCount <= 3],
			}).Update()
		},
	)
}

func HandleAssetAnalysisWillFinishV2(asset entity.Assets) {
	var req *coze.RetrieveWorkflowsRunsHistoriesReq
	if err := gconv.Struct(asset.AnalysisCozeID, &req); err != nil {
		return
	}

	GetWorkflowRunHistoryUntilSuccess(*req,
		func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
			var response struct {
				Output string `json:"Output"`
			}
			gjson.Unmarshal([]byte(history.Histories[0].Output), &response)
			var analysisResponse AnalysisCallbackResponse
			glog.Info(context.Background(), "response.Output: ", response.Output)
			gjson.Unmarshal([]byte(response.Output), &analysisResponse)
			analysisResponse.Data.AnalysisStatus = cons.Success.Value
			gf.Model("assets").Where("uuid", asset.UUID).Data(analysisResponse.Data).Update()
			RunTopicsWorkflowV2(asset)
			// utils.PublishMessage(
			// 	asset.UserUUID,
			// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.ANALYSIS.Value, ChangeStatus: cons.Success.Value},
			// )
		},
		func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
			workflowRunHistory := new(coze_dto.CozeWorkflowExecuteStatusLog)
			gconv.Struct(history.Histories[0], workflowRunHistory)
			workflowRunHistory.UserUUID = asset.UserUUID
			workflowRunHistory.AssetsUUID = asset.UUID
			gf.Model("tech_coze_workflow_run_history").Save(workflowRunHistory)
			errorCount, err := gf.Model("tech_coze_workflow_run_history").Where("assets_uuid", asset.UUID).Where("user_uuid", asset.UserUUID).Count()
			if err != nil {
				errorCount = 0
			}
			gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
				"analysis_status": map[bool]string{true: cons.Failed.Value, false: cons.Error.Value}[errorCount <= 3],
			}).Update()
		},
	)
}

func RunTopicsWorkflow(originAsset entity.Assets) {
	var user entity.User
	err := gf.Model("user").Scan(&user, "uuid", originAsset.UserUUID)
	if err != nil {
		gf.Log().Print(gctx.New(), "获取用户信息失败", err)
		return
	}

	var asset entity.Assets
	err1 := gf.Model("assets").Scan(&asset, "uuid", originAsset.UUID)
	if err1 != nil {
		gf.Log().Print(gctx.New(), "获取素材信息失败", err1)
		return
	}

	var gfctx = gctx.New()
	var workflowId, _ = gf.Cfg().Get(gfctx, "coze.default.topicGeneration.workflowId")
	var data = coze_dto.TopicsWorkflowParams{
		AnalysisResults: asset.AnalysisResults,
		AsrText:         asset.AsrText,
		VideoTitle:      asset.VideoTitle,
		CommercialPos:   user.CommercialPos,
		AssetsUUID:      asset.UUID,
	}

	AsyncRunWorkflow(coze.RunWorkflowsReq{
		WorkflowID: workflowId.String(),
		Parameters: gconv.Map(data),
		IsAsync:    true,
	}, func(resp *coze.RunWorkflowsResp) {
		var workflowParams = coze.RetrieveWorkflowsRunsHistoriesReq{
			WorkflowID: workflowId.String(),
			ExecuteID:  resp.ExecuteID,
		}

		var topics_generation_coze_id, _ = gjson.Marshal(entity.LocalRetrieveWorkflowsRunsHistoriesRes{
			RetrieveWorkflowsRunsHistoriesReq: workflowParams,
			DebugURL:                          resp.DebugURL,
		})
		gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
			"generation_status":         cons.Doing.Value,
			"topics_generation_coze_id": topics_generation_coze_id,
		}).Update()
		// utils.PublishMessage(
		// 	asset.UserUUID,
		// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.GENERATION.Value, ChangeStatus: cons.Doing.Value},
		// )
	})
}

func RunTopicsWorkflowV2(originAsset entity.Assets) {
	var user entity.User
	err := gf.Model("user").Scan(&user, "uuid", originAsset.UserUUID)
	if err != nil {
		gf.Log().Print(gctx.New(), "获取用户信息失败", err)
		return
	}

	var asset entity.Assets
	err1 := gf.Model("assets").Scan(&asset, "uuid", originAsset.UUID)
	if err1 != nil {
		gf.Log().Print(gctx.New(), "获取素材信息失败", err1)
		return
	}

	var gfctx = gctx.New()
	var workflowId, _ = gf.Cfg().Get(gfctx, "assets.default.topicGeneration.workflowId")
	var callbackUrl, _ = gf.Cfg().Get(gfctx, "assets.default.analysis.callbackUrl")
	uri, err := url.Parse(callbackUrl.String())
	if err != nil {
		return
	}
	v := url.Values{}
	v.Add("uuid", asset.UUID)
	v.Add("type", cons.GENERATION.Value)
	uri.RawQuery = v.Encode()

	var data = coze_dto.TopicsWorkflowParamsV2{
		CallbackUrl:    uri.String(),
		VisionAnalysis: asset.AnalysisResults,
		TopicTitle:     asset.TopicTitle,
		ScriptType:     asset.ScriptType,
		VideoTitle:     asset.VideoTitle,
		CommercialPos:  user.CommercialPos,
		UserAsk:        asset.LastUserAsk,
	}

	AsyncRunWorkflow(coze.RunWorkflowsReq{
		WorkflowID: workflowId.String(),
		Parameters: gconv.Map(data),
		IsAsync:    true,
	}, func(resp *coze.RunWorkflowsResp) {
		var workflowParams = coze.RetrieveWorkflowsRunsHistoriesReq{
			WorkflowID: workflowId.String(),
			ExecuteID:  resp.ExecuteID,
		}

		var topics_generation_coze_id, _ = gjson.Marshal(entity.LocalRetrieveWorkflowsRunsHistoriesRes{
			RetrieveWorkflowsRunsHistoriesReq: workflowParams,
			DebugURL:                          resp.DebugURL,
		})
		gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
			"generation_status":         cons.Doing.Value,
			"topics_generation_coze_id": topics_generation_coze_id,
		}).Update()
		// utils.PublishMessage(
		// 	asset.UserUUID,
		// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.GENERATION.Value, ChangeStatus: cons.Doing.Value},
		// )
	})
}

func HandleAssetTopicsWillFinish(asset entity.Assets) {
	gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish")
	var req *coze.RetrieveWorkflowsRunsHistoriesReq
	if err := gconv.Struct(asset.TopicsGenerationCozeID, &req); err != nil {
		gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish error", err)
		return
	}
	gf.Log().Print(gctx.New(), "before HandleAssetTopicsWillFinish GetWorkflowRunHistoryUntilSuccess")
	GetWorkflowRunHistoryUntilSuccess(*req, func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
		gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish GetWorkflowRunHistoryUntilSuccess")
		var response struct {
			Output string `json:"Output"`
		}
		gjson.Unmarshal([]byte(history.Histories[0].Output), &response)
		var topicListResponse TopicListResponse
		gjson.Unmarshal([]byte(response.Output), &topicListResponse)

		var usedList = garray.NewArray()
		for _, topic := range topicListResponse.TopicList {
			topic.UUID = gf.GenerateUUID()
			topic.UserUUID = asset.UserUUID
			topic.AssetsUUID = asset.UUID
			usedList.Append(topic)
		}
		if usedList.Len() != 0 {
			_, err := gf.Model("topics").Save(usedList.Slice())
			if err != nil {
				gf.Log().Print(gctx.New(), "保存选题失败", err)
				return
			}
		}
		gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
			"generation_status": map[bool]string{true: cons.Success.Value, false: cons.Failed.Value}[usedList.Len() != 0],
		}).Update()
		// utils.PublishMessage(
		// 	asset.UserUUID,
		// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.GENERATION.Value, ChangeStatus: cons.Success.Value},
		// )
	},
		func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
			gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish GetWorkflowRunHistoryUntilSuccess failed")
			workflowRunHistory := new(coze_dto.CozeWorkflowExecuteStatusLog)
			gconv.Struct(history.Histories[0], workflowRunHistory)
			workflowRunHistory.UserUUID = asset.UserUUID
			workflowRunHistory.AssetsUUID = asset.UUID
			gf.Model("tech_coze_workflow_run_history").Save(workflowRunHistory)
			errorCount, err := gf.Model("tech_coze_workflow_run_history").Where("assets_uuid", asset.UUID).Where("user_uuid", asset.UserUUID).Count()
			if err != nil {
				errorCount = 0
			}
			gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
				"generation_status": map[bool]string{true: cons.Failed.Value, false: cons.Error.Value}[errorCount <= 3],
			}).Update()
		})
}

func HandleAssetTopicsWillFinishV2(asset entity.Assets) {
	var req *coze.RetrieveWorkflowsRunsHistoriesReq
	if err := gconv.Struct(asset.TopicsGenerationCozeID, &req); err != nil {
		gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish error", err)
		return
	}
	GetWorkflowRunHistoryUntilSuccess(*req, func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
		gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish GetWorkflowRunHistoryUntilSuccess")
		var response struct {
			Output string `json:"Output"`
		}
		gjson.Unmarshal([]byte(history.Histories[0].Output), &response)
		var generationResponse GenerationCallbackResponse
		gjson.Unmarshal([]byte(response.Output), &generationResponse)

		var usedList = garray.NewArray()
		for _, topic := range generationResponse.Data.TopicList {
			topic.UUID = gf.GenerateUUID()
			topic.UserUUID = asset.UserUUID
			topic.AssetsUUID = asset.UUID
			usedList.Append(topic)
		}
		if usedList.Len() != 0 {
			_, err := gf.Model("topics").Save(usedList.Slice())
			if err != nil {
				gf.Log().Print(gctx.New(), "保存选题失败", err)
				return
			}
		}
		gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
			"generation_status": map[bool]string{true: cons.Success.Value, false: cons.Failed.Value}[usedList.Len() != 0],
		}).Update()
		// utils.PublishMessage(
		// 	asset.UserUUID,
		// 	&assets_vo.UserAssetsStatusEvent{AssetUUID: asset.UUID, ChangeType: cons.GENERATION.Value, ChangeStatus: cons.Success.Value},
		// )
	},
		func(history *coze.RetrieveWorkflowRunsHistoriesResp) {
			gf.Log().Print(gctx.New(), "HandleAssetTopicsWillFinish GetWorkflowRunHistoryUntilSuccess failed")
			workflowRunHistory := new(coze_dto.CozeWorkflowExecuteStatusLog)
			gconv.Struct(history.Histories[0], workflowRunHistory)
			workflowRunHistory.UserUUID = asset.UserUUID
			workflowRunHistory.AssetsUUID = asset.UUID
			gf.Model("tech_coze_workflow_run_history").Save(workflowRunHistory)
			errorCount, err := gf.Model("tech_coze_workflow_run_history").Where("assets_uuid", asset.UUID).Where("user_uuid", asset.UserUUID).Count()
			if err != nil {
				errorCount = 0
			}
			gf.Model("assets").Where("uuid", asset.UUID).Data(map[string]interface{}{
				"generation_status": map[bool]string{true: cons.Failed.Value, false: cons.Error.Value}[errorCount <= 3],
			}).Update()
		})
}

// JointVisionAnalysis 拼接视频分析结果和音频转文字内容
func JointVisionAnalysis(asset entity.Assets) string {
	result := asset.AnalysisResults

	// 添加视频原文部分
	result += "\n\n# 视频原文\n```" + asset.AsrText + "```"

	return result
}
