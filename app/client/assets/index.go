package assets

import (
	"crypto/md5"
	"fmt"
	"gofly/app/client/cons"
	assets_dto "gofly/app/client/dto/assets"
	"gofly/app/client/entity"
	"gofly/app/client/validators"
	assets_vo "gofly/app/client/vo/assets"
	"gofly/service/assets/utils"
	"gofly/service/assets/workflow"
	"gofly/service/assets/workflow/handler"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gconv"
	"gofly/utils/tools/gctx"
	"gofly/utils/tools/gjson"
	"gofly/utils/tools/gmap"
	"gofly/utils/tools/gstr"

	assets_utils "gofly/app/client/assets/utils"

	"github.com/samber/lo"
)

type Index struct{}

func init() {
	fpath := Index{}
	gf.Register(&fpath, fpath)
}

func (api *Index) Add(c *gf.GinCtx) {
	var dto assets_dto.TopicsWorkflowDTO

	if err := c.ShouldBindJSON(&dto); err != nil {
		gf.Failed().SetMsg(err.Error()).Regin(c)
		return
	}

	if err := gf.Validator().Assoc(dto).Data(dto).Locale("zh-CN").Run(c); err != nil {
		gf.Failed().SetMsg(err.String()).Regin(c)
		return
	}

	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	var (
		ctx      = gctx.New()
		user     = entity.User{}
		userRule = validators.UserValidatorOnlyCommercialPos{}
		err      error
	)
	err = gf.Model("user").Scan(&user, "uuid", userUUID)
	if err != nil {
		gf.Failed().SetMsg("用户获取失败").Regin(c)
		return
	}
	if err := gf.Validator().Assoc(user).Data(userRule).Locale("zh-CN").Run(c); err != nil {
		var code = cons.UserInfoCommercialPosNotComplete.Value
		gf.Failed().SetMsg(err.String()).SetCode(code).Regin(c)
		return
	}

	var dtoText, _ = gjson.Marshal(dto)
	var hashedDto = md5.Sum([]byte(dtoText))

	var key = fmt.Sprintf("ASSETS_ADD_%s_%s", userUUID, hashedDto)

	isExist, _ := gf.Redis().Do(ctx, "EXISTS", key)
	if isExist.Int() == 1 {
		gf.Failed().SetMsg("重复提交 请稍后再试").Regin(c)
		return
	} else {
		err = gf.Redis().SetEX(ctx, key, 1, 1*60)
		if err != nil {
			gf.Log().Print(c, "redis set error", err)
			// 当Redis出现错误时，返回空数据而不是直接return
			gf.Success().SetMsg("添加成功").SetData(nil).Regin(c)
			return
		}
	}

	var assetUUID = gf.GenerateUUID()
	var asset = &entity.Assets{
		BaseEntity: entity.BaseEntity{
			UUID: assetUUID,
		},
		UserUUID:         userUUID,
		DouyinURL:        dto.DouyinUrl,
		Insight:          dto.Insight,
		Source:           cons.Manual.Value,
		AnalysisStatus:   cons.Preprocessing.Value,
		GenerationStatus: cons.Pending.Value,
	}

	err = utils.PreParseAssetVideoMetaInfo(asset)
	if err != nil {
		gf.Failed().SetMsg("链接解析失败 请重试").Regin(c)
		return
	}

	_, err = gf.Model("assets").Insert(asset)
	if err != nil {
		gf.Log().Print(c, err)
		gf.Failed().SetMsg("添加失败").Regin(c)
		return
	}
	einoExecuteRecord, err := workflow.GetOrCreateEinoExecuteRecord(asset, cons.EinoExecuteAsset.Value)
	if err != nil {
		gf.Failed().SetMsg(err.Error()).Regin(c)
		return
	}

	einoExecuteRecord.HandleStatus = &cons.Processing.Value
	go func() {
		workflow.HandleWorkflow(asset, &user, einoExecuteRecord)
	}()

	gf.Success().SetMsg("成功").SetData(asset).Regin(c)
}

// 从 text 中匹配到
// # 视频音频转译文本
// 并将后续内容都替换为空
func CleanAsrText(text string) string {
	// 找到 "# 视频音频转译文本" 的位置
	pattern := `# 视频音频转译文本`

	// 查找匹配位置
	index := gstr.Pos(text, pattern)

	// 如果找到了，则截取该位置之前的内容
	if index >= 0 {
		return gstr.TrimRight(text[:index])
	}

	// 如果没有找到，返回原文本
	return text
}

func (api *Index) GetList(c *gf.GinCtx) {
	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	pageNo := gconv.Int(c.DefaultQuery("page", "1"))
	pageSize := gconv.Int(c.DefaultQuery("pageSize", "10"))
	keyword := c.DefaultQuery("keyword", "")

	whereMap := gmap.New()
	whereMap.Set("is_deleted", 0)
	whereMap.Set("user_uuid", userUUID)
	var assets []*entity.Assets = make([]*entity.Assets, 0)
	MDB := gf.Model(assets).With(entity.Topic{}).Where(whereMap)

	// 如果keyword存在且不为空，添加模糊搜索条件
	if keyword != "" {
		MDB.Where(MDB.Builder().WhereOr("video_title LIKE ?", "%"+keyword+"%").WhereOr("script_type LIKE ?", "%"+keyword+"%"))
	}

	totalCount, _ := MDB.Clone().Count()
	MDB.Order("create_time desc").Page(pageNo, pageSize).Scan(&assets)

	awemeIdList := lo.Map(assets, func(asset *entity.Assets, index int) string { return asset.VideoID })
	einoExecuteIdList := lo.Map(assets, func(asset *entity.Assets, index int) *string { return asset.EinoExecuteId })
	einoExecuteNotNullIdList := lo.Filter(einoExecuteIdList, func(einoExecuteId *string, index int) bool { return einoExecuteId != nil })

	var awemeList []*entity.DouyinAweme
	err := setting.CrawlerModel(awemeList).WhereIn("aweme_id", awemeIdList).Scan(&awemeList)
	if err != nil {
		gf.Log().Print(c, "获取抖音视频信息失败: ", err)
		gf.Failed().SetMsg("获取抖音视频信息失败").Regin(c)
		return
	}
	awemeIdMap := lo.SliceToMap(awemeList, func(t *entity.DouyinAweme) (string, *entity.DouyinAweme) { return t.AwemeID, t })

	var einoExecuteRecord entity.EinoExecuteRecord
	results, err := gf.Model(einoExecuteRecord).WhereIn("uuid", einoExecuteNotNullIdList).Fields("uuid", "source_type", "handle_status", "current_node_name").All()
	if err != nil {
		gf.Log().Print(c, "获取关联任务失败: ", err)
		gf.Failed().SetMsg("获取关联任务失败").Regin(c)
		return
	}

	einoExecuteRecordMap := make(map[string]*entity.EinoExecuteRecord)
	for _, result := range results {
		var einoExecuteRecord *entity.EinoExecuteRecord
		result.Struct(&einoExecuteRecord)
		einoExecuteRecordMap[result["uuid"].String()] = einoExecuteRecord
	}

	// 使用 lo.Map 处理 assets，清理 AnalysisResults 并拼接
	processedAssets := lo.Map(assets, func(asset *entity.Assets, index int) *entity.Assets {
		// 清理 AnalysisResults 中的 ASR 文本
		asset.AnalysisResults = CleanAsrText(asset.AnalysisResults)

		// 使用 JointVisionAnalysis 方法拼接内容
		asset.AnalysisResults = assets_utils.JointVisionAnalysis(*asset)

		asset.DouyinAweme = awemeIdMap[asset.VideoID]
		if asset.EinoExecuteId != nil {
			asset.EinoExecuteRecord = einoExecuteRecordMap[*asset.EinoExecuteId]
		}

		return asset
	})

	gf.Success().SetMsg("获取列表").SetData(processedAssets).SetExdata(gf.Map{
		"pagination": gf.Map{
			"page":     pageNo,
			"pageSize": pageSize,
			"total":    totalCount,
		},
	}).Regin(c)
}

// Topic 代表单个选题
type Topic struct {
	Uuid             string `json:"uuid"`
	UserUUID         string `json:"user_uuid"`
	AssetsUUID       string `json:"assets_uuid"`
	ScriptType       string `json:"script_type"`
	TopicDescription string `json:"topic_description"`
	TopicTitle       string `json:"topic_title"`
}

func (api *Index) GetUnfinishedStats(c *gf.GinCtx) {
	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	whereMap := gmap.New()
	whereMap.Set("is_deleted", 0)
	whereMap.Set("user_uuid", userUUID)

	MDB := gf.Model("assets").
		Where(whereMap)

	list, err := MDB.
		Where(MDB.Builder().WhereOrIn("analysis_status", []string{cons.Doing.Value, cons.Pending.Value}).
			WhereOrIn("generation_status", []string{cons.Doing.Value, cons.Pending.Value})).
		Select()

	if err != nil {
		gf.Log().Print(c, err)
		// Return 0 if there's an error
		gf.Failed().SetMsg("获取失败").Regin(c)
		return
	}

	m := gf.MapStrInt{
		"analysis":   0,
		"generation": 0,
	}
	for _, item := range list {
		var isAnalysisSuccess = gstr.Equal(item["analysis_status"].String(), cons.Success.Value)
		if isAnalysisSuccess {
			m["analysis"] = m["analysis"] + 1
		}

		m["generation"] = m["generation"] + 1
	}

	gf.Success().SetMsg("获取未处理素材数量成功").SetData(m).Regin(c)
}

// func (api *Index) DemoSendAssetsChanged(c *gf.GinCtx) {
// 	getuser, _ := c.Get("user")
// 	userOBJ := getuser.(gf.UserObj)
// 	userUUID := string(userOBJ.UserUUID)
// 	param, _ := gf.RequestParam(c)
// 	gf.Success().SetMsg("成功").SetData(param).Regin(c)

// 	var message *assets_vo.UserAssetsStatusEvent
// 	gconv.Struct(param, &message)

// 	redis_utils.PublishMessage(
// 		userUUID,
// 		message,
// 	)
// }

const SSE_MESSAGE_TEMPLATE = "data: %s\n\n"

func (api *Index) GetStartAssetsStatusStream(c *gf.GinCtx) {
	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)
	// Set headers for SSE
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Header().Set("Access-Control-Allow-Origin", "*")

	var (
		ctx = gctx.New()
	)
	conn, _ := gf.Redis().Conn(ctx)
	defer conn.Close(ctx)
	done := make(chan bool)
	_, err := conn.Subscribe(ctx, userUUID)
	if err != nil {
		fmt.Fprintf(c.Writer, SSE_MESSAGE_TEMPLATE, gconv.String(assets_vo.UserAssetsStatusEvent{EventType: assets_vo.Error.Value}))
		c.Writer.Flush()
		done <- true
		return
	}
	fmt.Fprintf(c.Writer, SSE_MESSAGE_TEMPLATE, gconv.String(assets_vo.UserAssetsStatusEvent{EventType: assets_vo.Start.Value}))
	c.Writer.Flush()

	go func() {
		for {
			gf.Log().Print(c, "ReceiveMessage before")
			msg, err := conn.ReceiveMessage(ctx)

			if err != nil {
				gf.Log().Print(c, "ReceiveMessage error", err)
				return
			}

			gf.Log().Print(c, msg.Payload)
			fmt.Fprintf(c.Writer, SSE_MESSAGE_TEMPLATE, msg.Payload)
			c.Writer.Flush()

			gf.Log().Print(c, msg)

			var event *assets_vo.UserAssetsStatusEvent
			gconv.Struct(msg.Payload, &event)

			if event.EventType == assets_vo.End.Value {
				done <- true
			}
		}
	}()

	// Wait for completion or client disconnect
	select {
	case <-done:
		close(done)
		return
	case <-c.Request.Context().Done():
		gf.Log().Print(c, "用户断开连接")
		close(done)
		return
	}
}

func (api *Index) RegenerateTopic(c *gf.GinCtx) {
	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	var dto assets_dto.RegenerateTopicDTO
	if err := c.ShouldBindJSON(&dto); err != nil {
		gf.Failed().SetMsg(err.Error()).Regin(c)
		return
	}

	var asset entity.Assets
	MDB := gf.Model(asset).Where("uuid", dto.UUID).Where("user_uuid", userUUID)
	MDB.Clone().Scan(&asset)
	if asset.UUID == "" {
		gf.Failed().SetMsg("素材不存在").Regin(c)
		return
	}

	var user entity.User
	err := gf.Model(user).Where("uuid", userUUID).Scan(&user)
	if err != nil {
		gf.Failed().SetMsg(err.Error()).Regin(c)
		return
	}

	asset.LastUserAsk = dto.UserAsk
	// asset.ScriptType = dto.ScriptType

	var existingRecord *entity.EinoExecuteRecord
	err = gf.Model(existingRecord).
		Where("uuid", asset.EinoExecuteId).
		Scan(&existingRecord)
	if err != nil {
		gf.Failed().SetMsg("获取Eino执行记录失败:").Regin(c)
		return
	}
	if existingRecord.HandleStatus != nil && *existingRecord.HandleStatus == cons.Processing.Value {
		gf.Failed().SetMsg("素材正在处理中").Regin(c)
		return
	}

	asset.EinoExecuteId = nil
	gf.Model(asset).Where("uuid", asset.UUID).Update(asset)
	einoExecuteRecord, err := workflow.GetOrCreateEinoExecuteRecord(&asset, cons.EinoExecuteAssetRegenerateTopics.Value)
	if err != nil {
		gf.Failed().SetMsg(err.Error()).Regin(c)
		return
	}

	einoExecuteRecord.HandleStatus = &cons.Processing.Value
	input := &handler.FlowStepStartCozeGenerationInput{
		AssetsUUID:      asset.UUID,
		AnalysisResults: asset.AnalysisResults,
		VideoTitle:      asset.VideoTitle,
		TopicTitle:      asset.TopicTitle,
		ScriptType:      dto.ScriptType,
		CommercialPos:   user.CommercialPos,
		UserAsk:         asset.LastUserAsk,
		AsrText:         asset.AsrText,
	}
	workflow.HandleRegenerateWorkflow(&asset, &user, einoExecuteRecord, input)

	gf.Success().SetMsg("开始重新生成").Regin(c)
}
