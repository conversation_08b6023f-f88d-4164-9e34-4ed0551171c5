package user

import (
	"fmt"
	"gofly/app/client/cons"
	"gofly/app/client/entity"
	"gofly/app/client/entity/services"
	"gofly/utils/gf"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestHandleSingleUserSyncCollection_VideoRelatedCreation 测试收藏夹同步时创建视频关联记录
func TestHandleSingleUserSyncCollection_VideoRelatedCreation(t *testing.T) {
	// 注意：这是一个集成测试，需要数据库连接
	// 在实际环境中运行时需要确保数据库配置正确

	t.Run("测试视频关联记录创建逻辑", func(t *testing.T) {
		// 创建测试用户
		testUser := entity.User{
			DouyinCookie: "test-cookie", // 实际测试时需要有效的cookie
		}
		testUser.UUID = "test-user-uuid-12345"

		// 模拟 aweme_id 列表
		testAwemeIds := []string{
			"7123456789012345678",
			"7123456789012345679",
			"7123456789012345680",
		}

		// 创建视频关联服务
		videoRelatedService := services.NewUserInboxVideoRelatedService()

		// 测试批量创建功能
		sourceId := "test-collect-123"
		err := videoRelatedService.BatchCreateFromCollectSync(testUser.UUID, sourceId, testAwemeIds)

		// 在没有数据库连接的情况下，这个测试会失败
		// 但我们可以验证函数调用不会panic
		if err != nil {
			t.Logf("预期的数据库连接错误: %v", err)
		}

		// 验证去重逻辑 - 再次调用相同的数据
		err2 := videoRelatedService.BatchCreateFromCollectSync(testUser.UUID, sourceId, testAwemeIds)
		if err2 != nil {
			t.Logf("预期的数据库连接错误 (去重测试): %v", err2)
		}
	})

	t.Run("测试空aweme_id列表处理", func(t *testing.T) {
		testUser := entity.User{}
		testUser.UUID = "test-user-uuid-empty"

		videoRelatedService := services.NewUserInboxVideoRelatedService()

		// 测试空列表
		err := videoRelatedService.BatchCreateFromCollectSync(testUser.UUID, "test-source", []string{})
		assert.NoError(t, err, "空列表应该正常处理")

		// 测试nil列表
		err = videoRelatedService.BatchCreateFromCollectSync(testUser.UUID, "test-source", nil)
		assert.NoError(t, err, "nil列表应该正常处理")
	})
}

// TestUserInboxVideoRelated_EntityValidation 测试实体验证
func TestUserInboxVideoRelated_EntityValidation(t *testing.T) {
	t.Run("测试有效的实体创建", func(t *testing.T) {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     "test-user-uuid",
			SourceId:     "123456789",
			AwemeId:      "7123456789012345678",
			SourceType:   entity.SourceTypeCollect,
			HandleStatus: cons.Pending.Value,
		}

		err := record.BeforeCreate()
		assert.NoError(t, err)
		assert.NotEmpty(t, record.UUID)
		assert.Equal(t, entity.SourceTypeCollect, record.SourceType)
		assert.Equal(t, cons.Pending.Value, record.HandleStatus)
		assert.Len(t, record.UUID, 32, "UUID应该是32位字符串")
	})

	t.Run("测试UUID唯一性", func(t *testing.T) {
		// 创建多个记录，验证UUID不重复
		uuids := make(map[string]bool)
		for i := 0; i < 100; i++ {
			record := &entity.UserInboxVideoRelated{
				UserUUID:     "test-user-uuid",
				SourceId:     "123456789",
				AwemeId:      fmt.Sprintf("712345678901234567%d", i),
				SourceType:   entity.SourceTypeCollect,
				HandleStatus: cons.Pending.Value,
			}

			err := record.BeforeCreate()
			assert.NoError(t, err)
			assert.NotEmpty(t, record.UUID)

			// 检查UUID是否重复
			assert.False(t, uuids[record.UUID], "UUID不应该重复: %s", record.UUID)
			uuids[record.UUID] = true
		}
		assert.Len(t, uuids, 100, "应该生成100个不同的UUID")
	})

	t.Run("测试无效的SourceType", func(t *testing.T) {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     "test-user-uuid",
			SourceId:     "123456789",
			AwemeId:      "7123456789012345678",
			SourceType:   "invalid_type",
			HandleStatus: cons.Pending.Value,
		}

		err := record.BeforeCreate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid source_type")
	})

	t.Run("测试无效的HandleStatus", func(t *testing.T) {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     "test-user-uuid",
			SourceId:     "123456789",
			AwemeId:      "7123456789012345678",
			SourceType:   entity.SourceTypeCollect,
			HandleStatus: "invalid_status",
		}

		err := record.BeforeCreate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid handle_status")
	})

	t.Run("测试必填字段验证", func(t *testing.T) {
		// 测试缺少UserUUID
		record1 := &entity.UserInboxVideoRelated{
			SourceId:     "123456789",
			AwemeId:      "7123456789012345678",
			SourceType:   entity.SourceTypeCollect,
			HandleStatus: cons.Pending.Value,
		}
		err1 := record1.BeforeCreate()
		assert.Error(t, err1)
		assert.Contains(t, err1.Error(), "user_uuid is required")

		// 测试缺少SourceId
		record2 := &entity.UserInboxVideoRelated{
			UserUUID:     "test-user-uuid",
			AwemeId:      "7123456789012345678",
			SourceType:   entity.SourceTypeCollect,
			HandleStatus: cons.Pending.Value,
		}
		err2 := record2.BeforeCreate()
		assert.Error(t, err2)
		assert.Contains(t, err2.Error(), "source_id is required")

		// 测试缺少AwemeId
		record3 := &entity.UserInboxVideoRelated{
			UserUUID:     "test-user-uuid",
			SourceId:     "123456789",
			SourceType:   entity.SourceTypeCollect,
			HandleStatus: cons.Pending.Value,
		}
		err3 := record3.BeforeCreate()
		assert.Error(t, err3)
		assert.Contains(t, err3.Error(), "aweme_id is required")
	})
}

// TestVideoRelatedService_Integration 测试服务集成
func TestVideoRelatedService_Integration(t *testing.T) {
	t.Run("测试服务创建", func(t *testing.T) {
		service := services.NewUserInboxVideoRelatedService()
		assert.NotNil(t, service)
	})

	t.Run("测试批量创建参数验证", func(t *testing.T) {
		service := services.NewUserInboxVideoRelatedService()

		// 测试空用户UUID
		err := service.BatchCreateFromCollectSync("", "source123", []string{"aweme1"})
		// 在没有数据库的情况下，这会因为数据库连接失败而不是参数验证失败
		// 但至少验证函数不会panic
		if err != nil {
			t.Logf("预期的错误: %v", err)
		}
	})
}

// BenchmarkVideoRelatedCreation 性能测试
func BenchmarkVideoRelatedCreation(b *testing.B) {
	service := services.NewUserInboxVideoRelatedService()
	testAwemeIds := make([]string, 100)
	for i := 0; i < 100; i++ {
		testAwemeIds[i] = gf.GenerateUUID()
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 注意：这个基准测试在没有数据库连接时会失败
		// 但可以测试函数调用的开销
		_ = service.BatchCreateFromCollectSync("test-user", "test-source", testAwemeIds)
	}
}
